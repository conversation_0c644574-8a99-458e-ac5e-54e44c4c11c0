<template>
  <div class="addAreaDialog">
    <el-dialog
      v-model="changeTransferOpen"
      title="修改中转站信息"
      width="50%"
      :close-on-click-modal="false"
      @open="getOpenDeliveryInfo"
      @close="closeChange"
    >
      <el-form
        label-width="auto"
        class="areaForm"
        :model="totalFields"
        :rules="rules"
        ref="formRef"
      >
        <el-form-item label="中转站名称">
          {{ props.transitDepotName }}
        </el-form-item>
        <el-form-item label="所属班组" prop="teamName">
          <el-select
            style="width: 200px"
            placeholder="请选择"
            v-model="totalFields.teamName"
            @change="getDeliveryInfo"
          >
            <el-option label="无" value="无" />
            <el-option
              :label="item.teamName"
              :value="item.teamName"
              v-for="item in store.transferInfo"
              :key="item.teamName + '9'"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="启用状态" prop="status">
          <el-select
            style="width: 200px"
            placeholder="请选择"
            v-model="totalFields.status"
          >
            <el-option label="启用" value="启用" />
            <el-option label="禁用" value="禁用" />
          </el-select>
        </el-form-item>
        <el-form-item label="配送类型:">
          <div>{{ props.deliveryType }}</div>
        </el-form-item>
        <el-form-item label="对接配送域" prop="deliveryName">
          <el-select
            style="width: 200px"
            multiple
            placeholder="请选择"
            v-model="totalFields.deliveryName"
            @change="changeDelivery"
          >
            <el-option label="无" value="无" />
            <el-option
              :label="item"
              :value="item"
              v-for="item in getDeliveryName"
              :key="item + '9'"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="经度" prop="longitude">
          <el-input
            style="width: 200px"
            placeholder="点击输入"
            v-model="totalFields.longitude"
          />
        </el-form-item>
        <el-form-item label="纬度" prop="latitude">
          <el-input
            style="width: 200px"
            placeholder="点击输入"
            v-model="totalFields.latitude"
          />
        </el-form-item>
      </el-form>

      <div class="btns">
        <el-button type="primary" @click="closeChange">取消</el-button>
        <el-button
          type="primary"
          style="margin-left: 100px"
          @click="confirmChange"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { ref } from "vue";
  import { useTransferStore } from "@/store/managerment/transfer";
  import { ITransitDepotUpdateData } from "@/types/transfer";
  const store = useTransferStore();
  const changeTransferOpen = ref(false);
  const isTeamBlank = ref<boolean>(false);
  const props = defineProps([
    "transitDepotName",
    "transitDepotId",
    "deliveryType",
    "info",
  ]);
  const formRef = ref<any>();
  defineExpose({
    changeTransferOpen,
  });
  const validateStatus = (rule: any, value: any, callback: any) => {
    if (value === "启用" && isTeamBlank.value) {
      callback(new Error("班组为无!"));
    } else {
      formRef.value.clearValidate("status");
      callback();
    }
  };

  const rules = reactive({
    status: [
      {
        validator: validateStatus,
        trigger: "change",
      },
    ],
  });

  const totalFields = ref<any>({
    deliveryName: [],
    latitude: "",
    longitude: "",
    status: "",
    teamName: "",
  });

  onUpdated(() => {
    totalFields.value.teamName = props.info.groupName;
    totalFields.value.deliveryName =
      props.info.deliveryName.length == 0
        ? []
        : props.info.deliveryName.split(",");
    totalFields.value.status = props.info.status;
    totalFields.value.latitude = props.info.latitude;
    totalFields.value.longitude = props.info.longitude;
  });

  const addEmit = defineEmits(["confirmChange"]);

  const closeChange = () => {
    formRef.value.resetFields();
    changeTransferOpen.value = false;
  };

  async function confirmChange() {
    if (!formRef.value) return;
    await formRef.value.validate((valid: any) => {
      if (valid) {
        if (totalFields.value.teamName === "无") {
          totalFields.value.teamName = "";
        }
        if (
          totalFields.value.status === "启用" &&
          totalFields.value.deliveryName.length === 0
        ) {
          ElMessage.error("状态为启用时, 配送域不能为空!");
          return;
        }
        store
          .updateTransitDepotData(toChangeData(totalFields.value))
          .then((res) => {
            formRef.value.resetFields();
            changeTransferOpen.value = false;
            if (res.code === 50001 || res.message === "系统异常") {
              ElMessage.error("系统异常!");
              return;
            }
            addEmit("confirmChange");
          });
      } else {
        ElMessage({
          message: "修改失败",
          type: "warning",
        });
      }
    });
  }

  function toChangeData(data: any): ITransitDepotUpdateData {
    const changeData = {
      ...data,
      deliveryType: props.deliveryType,
      transitDepotId: Number(props.transitDepotId),
      transitDepotName: props.transitDepotName,
    };
    changeData.deliveryName = totalFields.value.deliveryName.join(",");
    return changeData;
  }

  function getOpenDeliveryInfo() {
    const team = store.transferInfo.find(
      (item: any) => item.teamName === totalFields.value.teamName
    );
    team.deliveryName.split(",").forEach((ele: any) => {
      if (getDeliveryName.value.findIndex((item: any) => item === ele) == -1) {
        getDeliveryName.value.push(ele);
      }
    });
  }

  const getDeliveryName = ref<string[]>([]);

  function getDeliveryInfo() {
    if (!totalFields.value.teamName || totalFields.value.teamName === "无") {
      getDeliveryName.value = [];
      totalFields.value.deliveryType = "";
      isTeamBlank.value = true;
      return;
    }
    formRef.value.clearValidate("status");
    isTeamBlank.value = false;
    getDeliveryName.value = [];
    totalFields.value.deliveryName = [];
    store.transferInfo.forEach((item: any) => {
      if (item.teamName === totalFields.value.teamName) {
        item.deliveryName.split(",").forEach((ele: any) => {
          if (!(ele === "" || !ele || ele === "null")) {
            getDeliveryName.value.push(ele);
          }
        });
      }
    });
    const info = store.transferInfo.find(
      (item: any) => totalFields.value.teamName === item.teamName
    );
    totalFields.value.deliveryType = info.deliveryType;
  }

  function changeDelivery() {
    if (totalFields.value.deliveryName.includes("无")) {
      totalFields.value.deliveryName = ["无"];
    }
  }
</script>

<style lang="less" scoped>
  .addArea {
    color: black;
  }
  .areaForm {
    margin-left: 100px;

    .areaItem {
      font-size: 20px;
    }
  }
  .btns {
    display: flex;
    justify-content: center;
    color: black;
  }
</style>
