<template>
  <el-dialog
    v-model="noteVisible"
    title="导入日志"
    class="transform"
    width="50%"
    align-center
    :close-on-click-modal="false"
    @close="onDialogClose"
  >
    <div style="font-size: 20px">最近6个月的导入记录</div>
    <el-table
      ref="tableRef"
      :data="tableData"
      v-if="tableData"
      :cell-style="{ textAlign: 'center' }"
      :header-cell-style="{
        height: '2vh',
        'text-align': 'center',
      }"
      size="small"
      :row-style="{ height: '4.3vh' }"
      style="font-size: 1vw"
    >
      <el-table-column label="文件名" min-width="1%" prop="fileName" />
      <el-table-column label="大小" min-width="1%" prop="fileSize" />
      <el-table-column label="导入时间" min-width="1%" prop="importTime" />
      <el-table-column label="用户" min-width="1%" prop="userName" />
      <el-table-column label="状态" min-width="1%" prop="status" />
      <el-table-column label="操作" min-width="2%">
        <template #default="scope">
          <el-button
            size="small"
            type="primary"
            @click="downloadLog(scope.row.fileName, scope.row.importTime)"
            v-if="!scope.row.status.includes('全部导入成功')"
            :icon="Download"
          >
          </el-button>
          <el-button
            size="small"
            type="primary"
            @click="deleteLog(Number(scope.row.fileId))"
            :icon="Delete"
          >
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="pageDivide">
      <el-pagination
        v-if="current"
        layout="prev, pager, next"
        :current-page="searchData.pageNum"
        :page-size="searchData.pageSize"
        :total="Number(current.total)"
        @current-change="handlePageChange"
      />
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
  import { useLocationStore } from "@/store/pos";
  import { Delete } from "@element-plus/icons-vue";
  import { Download } from "@element-plus/icons-vue";
  const store = useLocationStore();
  const noteVisible = ref<boolean>(false);
  const tableRef = ref<any>();
  const tableData = ref<any>();
  const current = ref<any>({});
  const searchData = reactive({
    pageNum: 1,
    pageSize: 6,
  });

  defineExpose({
    noteVisible,
    onOpenDialog,
  });

  function onOpenDialog() {
    searchData.pageNum = 1;
    getData();
  }

  function onDialogClose() {
    current.value = null;
  }

  function downloadLog(fileName: string, importTime: string) {
    store.downloadLocationLog({ fileName, importTime }).then((res) => {
      let a = document.createElement("a");
      a.download = fileName;
      a.style.display = "none";
      let url = URL.createObjectURL(res);
      a.href = url;
      document.body.appendChild(a);
      a.click();
      URL.revokeObjectURL(url); // 销毁
      document.body.removeChild(a);
    });
  }

  function deleteLog(logsId: number) {
    store.deleteLocationLog({ logsId }).then(() => {
      ElMessage.success("删除成功");
      getData();
    });
  }
  //处理分页
  function handlePageChange(num: number = 1) {
    searchData.pageNum = num;
    getData();
  }

  function getData() {
    store.importLog({ ...searchData, type: "3" }).then((res: any) => {
      current.value = res;
      searchData.pageNum = res.current;
      tableData.value = res.records;
    });
  }
</script>

<style lang="less" scoped>
  .transform {
    .content {
      display: flex;
      justify-content: center;
      font-size: 20px;
    }
  }

  :deep(.el-upload-dragger) {
    height: 30vh;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
  }
</style>
