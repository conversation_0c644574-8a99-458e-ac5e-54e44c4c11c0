<template>
  <el-dialog
    v-model="dialogVisible"
    title="特殊点标记"
    width="400px"
    :close-on-click-modal="false"
    :show-close="true"
    @close="handleClose"
  >
    <div class="special-point-form">
      <el-form :model="formData" label-width="80px">
        <el-form-item label="标签:">
          <el-input
            :show-word-limit="true"
            :maxlength="20"
            v-model="formData.remark"
            placeholder="请输入标签"
          ></el-input>
        </el-form-item>
        <el-form-item label="类型:">
          <el-input v-model="formData.type" placeholder="请输入类型"></el-input>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
  // 导入所需的Vue API
  import { ref, reactive, defineExpose } from "vue";
  import { ElMessage } from "element-plus";

  // 定义内部状态
  const dialogVisible = ref(false);
  const formData = reactive({
    remark: "",
    isSpecialPoint: "",
    storeId: [],
    type: "",
  });

  // 定义回调函数
  let confirmCallback = null;
  let cancelCallback = null;

  // 打开对话框的方法
  const open = (options = {}) => {
    // 设置默认值
    formData.remark = options.remark || "";
    formData.isSpecialPoint = options.isSpecialPoint || "0";
    formData.storeId = options.storeId || [];

    // 保存回调函数
    confirmCallback = options.onConfirm;
    cancelCallback = options.onCancel;

    // 显示对话框
    dialogVisible.value = true;
  };

  // 确认按钮处理函数
  const handleConfirm = () => {
    // 验证表单
    if (!formData.remark.trim()) {
      ElMessage.warning("请输入标签");
      return;
    }

    // 调用回调函数并传递参数
    if (typeof confirmCallback === "function") {
      const array = [];
      formData.storeId.forEach((item) => {
        array.push({
          remark: formData.remark,
          isSpecialPoint: formData.isSpecialPoint,
          storeId: item,
          specialType: formData.type,
        });
      });
      confirmCallback(array);
    }

    // 关闭对话框
    dialogVisible.value = false;
  };

  // 取消按钮处理函数
  const handleCancel = () => {
    if (typeof cancelCallback === "function") {
      cancelCallback();
    }
    dialogVisible.value = false;
  };

  // 关闭对话框处理函数
  const handleClose = () => {
    // 重置表单
    formData.remark = "";
    formData.isSpecialPoint = false;
    formData.storeId = [];
    formData.type = "";

    // 调用取消回调
    handleCancel();
  };

  // 暴露方法和参数给父组件
  defineExpose({
    open,
    formData,
  });
</script>

<style lang="less" scoped>
  .special-point-form {
    padding: 10px;
    :deep(.el-input) {
      .el-input__count {
        background-color: rgb(1, 22, 49) !important;
      }
      .el-input__count-inner {
        background-color: rgb(1, 22, 49) !important;
      }
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: space-between;
  }

  .dialog-footer .el-button {
    flex: 1;
    margin: 0 10px;
  }
</style>
