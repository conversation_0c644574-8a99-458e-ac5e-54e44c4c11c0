<template>
  <div class="route">
    <div class="aside">
      <BorderBox9
        :color="['#90ade8', '#90ade8']"
        backgroundColor="#001731"
        style="width: 100%"
      >
        <div class="leftInformation">
          <div class="left-container">
            <div class="lefttop">
              <div class="left-title">
                <p>历史版本</p>
                <p>当前版本: {{ currentVersion.versionName }}</p>
              </div>
              <div class="leftitem">
                <template
                  v-for="(item, index) in veritems.versionVos"
                  :key="item.id"
                >
                  <tItem
                    v-if="item.isShow != 0"
                    :key="item.versionId + '444'"
                    :versionId="item.versionId"
                    :versionName="item.versionName"
                    :updateTime="item.updateTime"
                    :versionDb="item.versionDb"
                    :status="getStatus(item.versionId)"
                    :isShow="item.isShow"
                    :versionInfo="item.versionInfo"
                    :isSelected="selectedVersionId == item.versionId"
                    @click="handleItemClick"
                    @rename="openEditVer"
                    @delete="openDeleteDialog"
                  />
                </template>
              </div>
            </div>
            <div class="button">
              <el-button 
                v-if="hasOp('version-management:save-new')"
                class="btn" 
                @click="openSaveDialog"
                >另存为新版本</el-button
              >
              <el-button
                class="btn"
                v-if="hasOp('version-management:enable') && currentVersion.versionName != selectedItem.versionName"
                @click="showVersionDiffDialog"
                >启用</el-button
              >
            </div>
          </div>
        </div>
      </BorderBox9>
    </div>
    <div class="map">
      <div class="btn-box">
        <div class="btnsbox">
          <el-select v-model="area">
            <el-option
              v-for="item in areas"
              :key="item.name"
              :label="item.name"
              :value="item.name"
            />
          </el-select>
          <el-input-number
            v-if="area !== '韶关市'"
            class="num"
            v-model="num"
            :min="1"
            :max="1000"
            :style="{ color: 'black', marginLeft: 20 + 'px' }"
          />
        </div>
        <div class="btnsbox">
          <el-button
            v-if="hasOp('path-calculate:recalculate')"
            class="btn"
            @click="CalculateBtnFunction"
            :loading="loadCalculate"
            >重新计算</el-button
          >
          <el-button
            v-if="hasOp('path-calculate:accumulation:update')"
            class="adjustPoint"
            @click="adjustPoint"
            :loading="adjustLoad"
            >{{ adjustText }}</el-button
          >
          <el-button
            class="adjustPoint"
            @click="refreshConvex"
            :loading="adjustLoad"
            >刷新缓存</el-button
          >
          <el-button
            class="adjustPoint"
            @click="exportMessage"
            :loading="adjustLoad"
            >导出路径</el-button
          >
        </div>
      </div>
      <!--  -->
      <div id="container">
        <el-button class="icon" @click="openEChart">
          <el-icon color="black">
            <Edit />
          </el-icon>
        </el-button>
        <div class="position-message" v-if="appearPositionMessage">
          <template
            v-for="(item, index) in clusterStore.storeResult"
            :key="index"
          >
            <div>{{ index + 1 }} : {{ item.accumulationAddress }}</div>
          </template>
        </div>
      </div>
      <!--  -->
      <RouteEChart
        :data="eChartData"
        :api-key="pathCalculateInfo.apiKey"
        v-model="isOpenEChart"
        @dis="disEchart"
        @tim="timEchart"
        @wei="weiEchart"
      ></RouteEChart>
    </div>
    <BorderBox9
      :color="['#90ade8', '#90ade8']"
      backgroundColor="#001731"
      style="margin-right: -1vw"
    >
      <div class="rightInformation">
        <div class="rtitle">{{ route }}</div>
        <div class="detailedRoute">
          <el-scrollbar height="68vh">
            <el-collapse
              v-model="activeNames2"
              v-loading="isResultPointsFinished"
              element-loading-text="加载中..."
              element-loading-background="rgba(0,23,49,0.8)"
            >
              <el-collapse-item
                :title="
                  item.groupName +
                  '-' +
                  item.carCount +
                  '辆车' +
                  '-' +
                  item.routeCount +
                  '条路线'
                "
                :name="item.groupId"
                v-for="item in clusterStore.routeDetails"
                :key="item.groupId"
              >
                <el-collapse
                  v-model="activeNames3"
                  class="routeNameLi"
                  v-for="item1 in item.list"
                  :key="item1.transitDepotId"
                >
                  <el-collapse-item
                    v-for="item2 in item1.routeList"
                    :key="item1.transitDepotId"
                    :title="item1.transitDepotName + '-' + item2.date"
                    :name="item2.date + item1.transitDepotId"
                  >
                    <ul>
                      <li
                        class="accumulationNameLi"
                        tabindex="1"
                        style="font-size: 14px"
                        v-for="item3 in item2.routeList"
                        :key="item3.routeId"
                        @click.stop="accumulationNameClick(item3)"
                        :class="item3.routeName == mapRouteName ? 'active' : ''"
                        :id="item3.routeName"
                      >
                        {{ item3.routeName }}
                      </li>
                    </ul>
                  </el-collapse-item>
                </el-collapse>
              </el-collapse-item>
            </el-collapse>
          </el-scrollbar>
        </div>
        <el-button class="saveRoute" @click="addRouteBtn" :loading="saveLoading"
          >保存路径</el-button
        >
        <el-dialog
          style="transform: translate(17.5vw, 48vh); height: 32vh"
          v-model="isOpenRouteDialog"
          width="20%"
          :modal="false"
          :before-close="closeRouteDialog"
          :append-to-body="true"
          :title="titleAccumulationName"
        >
          <el-scrollbar height="20vh">
            <ul style="margin-left: 20px; padding-top: 0px">
              <li
                style="padding: 0.3vh; list-style: none"
                v-for="(item3, index) in clusterStore.storeResult"
                :key="item3.accumulationId"
              >
                <div>{{ index + 1 }} : {{ item3.accumulationAddress }}</div>
                <!-- {{ item3.accumulationAddress }} -->
              </li>
            </ul>
          </el-scrollbar>
        </el-dialog>
      </div>
    </BorderBox9>
    <editVers ref="editDialogRef" :form-data="editVersion" @save="handleSave" />
    <deleteVersio ref="deleteDialogRef" @confirm="handleDeleteConfirm" />
    <versionDiff
      ref="versionDiffRef"
      :update-time="selectedItem.formattedTime"
      :status="selectedItem.status"
      :versionName="selectedItem.versionName"
      :versionId="selectedItem.versionId"
      @continue="handleContinue"
    />
  </div>
</template>

<script lang="ts" setup>
  import { useVersStore } from "@/store/ver";
  import versionDiff from "./cpn/enableVers.vue";
  import editVers from "./cpn/editVers.vue";
  import deleteVersio from "./cpn/deleteVersio.vue";
  import { MAP_KEY, SECURITY_CODE, MAP_API_KEY } from "@/utils/getMapKey";
  import { modifyUserAgent } from "@/utils/modifyUserAgent";
  import { hasOp } from "@/op";
  import { Edit, DArrowLeft, DArrowRight } from "@element-plus/icons-vue";
  import { BorderBox9 } from "@dataview/datav-vue3";
  //@ts-ignore
  import AMapLoader from "@amap/amap-jsapi-loader";
  import { useClusterStore } from "@/store/cluster";
  import RouteEChart from "./cpn/routeEChart.vue";
  import tItem from "./cpn/tItem.vue";
  import { data1, data2, data3, data4, data5 } from "./data/data";
  import colorArr from "./data/colorArr";
  window._AMapSecurityConfig = {
    securityJsCode: SECURITY_CODE,
  };
  // 点击图表
  let EchartFun: (groupData: any) => void;
  const verStore = useVersStore();
  //聚集区Store
  const clusterStore = useClusterStore();
  const versionDiffRef = ref();
  const editDialogRef = ref();
  const veritems = ref<any>([]);
  const selectedVersionId = ref<any>(null);
  const selectedItem = ref({
    versionId: "",
    updateTime: "",
    status: "",
    versionName: "",
    versionDb: "",
  });
  const currentVersion = ref<any>("");
  const editVersion = reactive<any>({
    versionName: "",
    remark: "",
  });
  const groupName = ref<string>();
  const routeIndex = ref<number>();
  const appearPositionMessage = ref<boolean>(false);

  const deleteDialogRef = ref<any>();
  // 处理项点击事件
  const handleItemClick = (itemData: any) => {
    // 如果点击已选中的版本，则取消选中
    if (selectedVersionId.value === itemData.versionId) {
      selectedVersionId.value = null;
    } else {
      // 否则选中点击的版本
      selectedVersionId.value = itemData.versionId;
    }
    selectedItem.value = itemData;
  };

  const getStatus = (versionId: any) => {
    if (versionId == currentVersion.value.versionId) {
      return true;
    }
    // 检查 versionId 是否在数组中
    const exists = veritems.value.versionVos.some(
      (item: any) => item.versionId === versionId
    );
    if (!exists) return false;

    // 返回 obj 中对应 versionId 的值
    return veritems.value.map[versionId];
  };

  const openDeleteDialog = (data: any) => {
    deleteDialogRef.value.openDialog(data.versionId);
  };

  const handleDeleteConfirm = (id: string) => {
    console.log(id);
    console.log("确认删除版本:", id);
    verStore.delVerData(id).then((res) => {
      console.log(res);
      if (res.msg == "删除成功") {
        getListVers();
        ElMessage.success("删除成功");
      } else {
        ElMessage.error("删除失败");
      }
    });
  };

  function openEditVer(data: any) {
    editVersion.versionId = data.versionId;
    editVersion.versionDb = data.versionDb;
    editVersion.versionName = data.versionName;
    editVersion.versionInfo = data.versionInfo;
    editDialogRef.value.openDialog(editVersion);
  }

  const handleSave = async (data: any) => {
    if (editDialogRef.value.confirmButtonName === "确定") {
      console.log(6);
      const updateRes: any = await verStore.updateVerData({
        versionId: data.versionId,
        versionName: data.versionName,
        versionInfo: data.versionInfo,
      });
      if (updateRes.code == "200") {
        ElMessage.success(updateRes.msg);
        getListVers();
      } else {
        ElMessage.error("保存失败");
      }
      return;
    }
    let loading = ElLoading.service({
      lock: true,
      text: "另存版本中...",
      background: "rgba(0, 0, 0, 0.7)",
    });
    const res: any = await verStore.saveNewVersionData({
      versionName: data.versionName,
      remake: data.versionInfo,
    });
    console.log(res);
    if (res.code == 200) {
      ElMessage.success(res.msg);
      getListVers();
    } else {
      ElMessage.error("另存失败");
    }
    loading.close();
  };

  function openSaveDialog() {
    if (
      veritems.value.versionVos.filter((item: any) => item.isShow == 1)
        .length >= 3
    ) {
      ElMessage.error("超出三个,必须删除一个");
      return;
    }
    editDialogRef.value.openDialog({}, true);
  }

  const showVersionDiffDialog = () => {
    if (!selectedItem.value.versionId) {
      ElMessage({
        message: "没有选中数据",
        type: "error",
      });
      return;
    }
    versionDiffRef.value.openDialog();
  };

  const handleContinue = async (versionId: string) => {
    verStore.changeVerData(versionId).then(async (res) => {
      if (res.code == 200) {
        await getListVers();
        ElMessage.success("启用成功");
      } else {
        ElMessage.error("启用失败");
      }
    });
  };

  function disEchart(groupData: any) {
    EchartFun(groupData);
    if (groupData.name.substring(0, 2) != "班组") {
      routeIndex.value = groupData.dataIndex;
    }
  }
  function timEchart(groupData: any) {
    EchartFun(groupData);
    if (groupData.name.substring(0, 2) != "班组") {
      routeIndex.value = groupData.dataIndex;
    }
  }
  function weiEchart(groupData: any) {
    EchartFun(groupData);
    if (groupData.name.substring(0, 2) != "班组") {
      routeIndex.value = groupData.dataIndex;
    }
  }
  modifyUserAgent();
  var map: any = null;
  AMapLoader.load({
    key: MAP_KEY, // 申请好的Web端开发者Key，首次调用 load 时必填
    version: "2.0", // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
    plugins: ["AMap.DistrictSearch", "AMap.MarkerCluster"], // 需要使用的的插件列表，如比例尺'AMap.Scale'等 // 需要使用的的插件列表，如比例尺'AMap.Scale'等
  }).then(async (AMap: any) => {
    // 创建一个DistrictSearch对象，用于搜索行政区划信息。
    const district = new AMap.DistrictSearch({
      subdistrict: 1,
      extensions: "all",
      level: "province",
    });
    // 使用DistrictSearch对象搜索"韶关市"的行政区划信息，并在回调函数中处理结果
    district.search("韶关市", async function (_: any, result: any) {
      const bounds = result.districtList[0].boundaries;
      const mask = [];
      for (let i = 0; i < bounds.length; i++) {
        mask.push([bounds[i]]);
      }

      map = new AMap.Map("container", {
        // 设置地图容器id
        mask: mask, // 为Map实例制定掩模的路径,各图层将值显示路径范围内图像,3D模式下有效
        zoom: 9, // 设置当前显示级别
        expandZoomRange: true, // 开启显示范围设置
        zooms: [9, 18], //最小显示级别为7，最大显示级别为20
        center: [113.767587, 24.718014], // 设置地图中心点位置
        viewMode: "3D", // 特别注意,设置为3D则其他地区不显示
        zoomEnable: true, // 是否可以缩放地图
        resizeEnable: true,
      });
      //分割线涂色
      let polygon: any[] = [
        "polygon1",
        "polygon2",
        "polygon3",
        "polygon4",
        "polygon5",
      ];
      let polygonData: any = [data1, data2, data3, data4, data5];
      for (let i = 0; i < polygon.length; i++) {
        polygon[i] = new AMap.Polyline({
          path: polygonData[i], // 路径
          strokeWeight: 1, // 线条宽度，默认为 2
          strokeColor: "#001731", // 线条颜色
          lineJoin: "round", // 折线拐点连接处样式
          zIndex: 100,
        });
      }
      map.add(polygon);

      function revisePolylineToMap(
        newPolyon: any,
        pathData: any,
        color: string,
        strokeWeight: number
      ) {
        newPolyon.setOptions({
          path: pathData,
          strokeWeight: strokeWeight,
          strokeColor: color,
        });
      }
      // 使用该函数来添加折线
      EchartFun = function EchartSplice(groupData: any) {
        switch (groupData.name) {
          case "班组一":
            groupName.value = "班组一";
            revisePolylineToMap(polygon[0], data1, groupData.color, 8);
            revisePolylineToMap(polygon[1], data2, "#001731", 1);
            revisePolylineToMap(polygon[2], data3, "#001731", 1);
            revisePolylineToMap(polygon[3], data4, "#001731", 1);
            revisePolylineToMap(polygon[4], data5, "#001731", 1);
            break;
          case "班组二":
            groupName.value = "班组二";
            revisePolylineToMap(polygon[0], data1, "#001731", 1);
            revisePolylineToMap(polygon[1], data1, groupData.color, 8);
            revisePolylineToMap(polygon[2], data3, "#001731", 1);
            revisePolylineToMap(polygon[3], data4, "#001731", 1);
            revisePolylineToMap(polygon[4], data5, "#001731", 1);
            break;
          case "班组三":
            groupName.value = "班组三";
            revisePolylineToMap(polygon[0], data1, "#001731", 1);
            revisePolylineToMap(polygon[1], data2, "#001731", 1);
            revisePolylineToMap(polygon[2], data3, groupData.color, 8);
            revisePolylineToMap(polygon[3], data4, "#001731", 1);
            revisePolylineToMap(polygon[4], data5, "#001731", 1);
            break;
          case "班组四":
            groupName.value = "班组四";
            revisePolylineToMap(polygon[0], data1, "#001731", 1);
            revisePolylineToMap(polygon[1], data2, "#001731", 1);
            revisePolylineToMap(polygon[2], data3, "#001731", 1);
            revisePolylineToMap(polygon[3], data4, groupData.color, 8);
            revisePolylineToMap(polygon[4], data5, "#001731", 1);
            break;
          case "班组五":
            groupName.value = "班组五";
            revisePolylineToMap(polygon[0], data1, "#001731", 1);
            revisePolylineToMap(polygon[1], data2, "#001731", 1);
            revisePolylineToMap(polygon[2], data3, "#001731", 1);
            revisePolylineToMap(polygon[3], data4, "#001731", 1);
            revisePolylineToMap(polygon[4], data5, groupData.color, 8);
            break;
        }
      };
      //绑定点击事件
      map.on("click", function (e: any) {
        // console.log("当前坐标：" + e.lnglat.getLng() + "," + e.lnglat.getLat());
        navigator.clipboard.writeText(
          e.lnglat.getLng() + "," + e.lnglat.getLat()
        );
      });
    });
  });
  const activeNames = ref();
  const activeNames2 = ref();
  const activeNames3 = ref();
  // 路径互动
  const mapRouteName = ref<string>();
  const area = ref("韶关市");
  const areas = ref<any>();
  clusterStore.getTransitDepotNameAction().then(() => {
    areas.value = clusterStore.areas;
  });
  const route = ref("路线详情");
  //楚鸿的key： 309bde1e73b984c7d8a87ab19255963c
  //我的key：   cdeaa7cd146a1a9612827190fb0e0962
  //黄健的key：	af42aaba236b2553c34fc11ed5c18311
  const pathCalculateInfo = ref({
    apiKey: MAP_API_KEY,
    areaName: area.value,
    assignNumber: 10,
  });
  const choiceCalculateType = ref(0);
  const num = ref(10);

  // 打卡 eChart
  const isOpenEChart = ref(false);
  function openEChart() {
    isOpenEChart.value = !isOpenEChart.value;
  }
  const eChartData = ref<{
    dis: number[];
    wei: number[];
    time: number[];
    groupDis: number[];
    groupWei: number[];
    groupTime: number[];
    routeName: string[];
    groupRouteName: string[];
    groupName?: string;
  }>({
    dis: [],
    wei: [],
    time: [],
    routeName: [],
    groupDis: [],
    groupTime: [],
    groupWei: [],
    groupRouteName: [],
  });
  onMounted(() => {
    getListVers();
    //获取当前版本
    setTimeout(() => {
      // 加载保存的地图数据
      if (!clusterStore.convex) {
        refreshConvex();
      } else {
        countPathResult();
      }
    }, 1000);
  });

  async function getListVers() {
    verStore.getVerListData().then(async (res) => {
      if (res.code == 200) {
        veritems.value = res.data;
        const curr = await verStore.getCurrentVerData();
        currentVersion.value = veritems.value.versionVos.find((item: any) => {
          return item.versionDb == curr.msg;
        });
      }
    });
  }

  const markers: Array<AMap.Text> = [];
  const points = ref<boolean>(false);
  const accumulationId = ref<string>();
  const routeId = ref<string>();
  const routeName = ref<string>();
  const oldRouteName = ref<string>();
  const clickRoute = ref<string>();
  function countPathResult() {
    // 绘制点
    clusterStore.getConvexPointAction().then(() => {
      clusterStore.convexPoint.forEach((point: any) => {
        const position = new AMap.Marker({
          position: new AMap.LngLat(point.longitude, point.latitude),
          title: point.routeName,
          content: `<div style="font-size: 8px;color: #bcbcbc;">◉</div>`,
          //@ts-ignore
          anchor: "center",
        });
        map.add(position);

        points.value = true;
        //@ts-ignore
        position.on("click", function () {
          if (adjustLoad.value) {
            accumulationId.value = point.accumulationId;
            oldRouteName.value = point.routeName;
          }
        });
      });
    });

    // 涂色渲染

    clusterStore.colorConvex?.forEach((item: string[], index) => {
      item.forEach((item1, index1) => {
        const coordinatesArray = item1.split(";");
        // 将每个字符串转换成数组格式
        // const formattedArray = coordinatesArray.map((coord) => `[${coord}]`);
        const path = coordinatesArray.map((item) => {
          const arr = item.split(",");
          return new AMap.LngLat(Number(arr[0]), Number(arr[1]));
        });
        let polygon = new AMap.Polygon({
          //@ts-ignore
          path, //路径
          fillColor: colorArr[index][index1 % 5], //多边形填充颜色
          strokeWeight: 1, //线条宽度，默认为 2
          strokeColor: "#fff", //线条颜色
          fillOpacity: 0.6,
        });
        map.add(polygon);
      });
    });
    watch(groupName, () => {
      eChartData.value.groupDis = [];
      eChartData.value.groupWei = [];
      eChartData.value.groupTime = [];
      eChartData.value.groupRouteName = [];
    });
    // 开始读
    clusterStore.convex?.forEach((item: any) => {
      function addData() {
        // distance: "109.63"
        eChartData.value.groupDis.push(Number(item.distance));
        // cargoWeight: "1326.00"
        eChartData.value.groupWei.push(Number(item.cargoWeight));
        // workTime: 6.15
        eChartData.value.groupTime.push(Number(item.workTime));
        // routeName: "新丰县中转站-粤F QB12345-星期一-2024.06.04-1.0-1.0-2.0-1.0-1.0-2.0"
        eChartData.value.groupRouteName.push(item.routeName);
        // groupName：班组一/二/三/四/五  => item.groupId = 1/2/3/4/5
        eChartData.value.groupName = groupName.value;
      }
      watch(groupName, () => {
        if (groupName.value == "班组一" && item.groupId == 1) {
          addData();
        } else if (groupName.value == "班组二" && item.groupId == 2) {
          addData();
        } else if (groupName.value == "班组三" && item.groupId == 3) {
          addData();
        } else if (groupName.value == "班组四" && item.groupId == 4) {
          addData();
        } else if (groupName.value == "班组五" && item.groupId == 5) {
          addData();
        }
      });
      // 凸包渲染
      const polygonPath = item.convex.map((item: any) => {
        return [item.longitude, item.latitude];
      });

      const activePolyOption = {
        path: polygonPath,
        strokeOpacity: 1,
        strokeColor: "black",
        fillOpacity: 0.5,
        strokeWeight: 5,
        fillColor: "skyblue",
        strokeStyle: "solid",
      };
      const unActivePolyOption = {
        path: polygonPath,
        strokeColor: "#eee",
        strokeWeight: 3,
        fillOpacity: 0,
        strokeStyle: "solid",
      };
      const unchosenPolyOption = {
        path: polygonPath,
        strokeColor: "#eee",
        strokeWeight: 1,
        fillOpacity: 0,
        strokeStyle: "dashed",
      };

      let polygon = new AMap.Polygon(unActivePolyOption);
      let isActive = false;
      let marker: any = null;

      polygon.on("click", (event: any) => {
        // console.log(item);

        clusterStore.getStoreDetailsAction(item.routeId).then((res) => {
          appearPositionMessage.value = true;

          // console.log(clusterStore.storeResult);

          clusterStore.storeResult?.forEach((iten: any, index: number) => {
            let location = [iten.longitude, iten.latitude];

            let circleMarker = new AMap.CircleMarker({
              center: location, //圆心
              radius: 8, //半径
              strokeColor: "white", //轮廓线颜色
              strokeWeight: 2, //轮廓线宽度
              strokeOpacity: 0.5, //轮廓线透明度
              fillColor: "rgba(0,0,255,1)", //圆点填充颜色
              fillOpacity: 1, //圆点填充透明度
              zIndex: 10, //圆点覆盖物的叠加顺序
            });
            map.add(circleMarker);

            let text = new AMap.Text({
              text: index + 1,
              anchor: "top-center", //设置文本标记锚点位置
              position: location, //点标记在地图上显示的位置
              style: {
                color: "black",
                "font-size": "10px",
              },
            });
            text.setMap(map); //将文本标记设置到地图上
          });
        });
        clickRoute.value = item.routeName;
        activeNames2.value = item.groupId;
        activeNames3.value = item.routeName.split("-")[2] + item.transitDepotId;
        document.getElementById(item.routeName)?.scrollIntoView();
        if (mapRouteName.value != item.routeName) {
          mapRouteName.value = item.routeName;
        }

        let index = eChartData.value.dis.indexOf(Number(item.distance));
        if (!isActive) {
          eChartData.value.dis.push(Number(item.distance));
          eChartData.value.wei.push(Number(item.cargoWeight));
          eChartData.value.time.push(Number(item.workTime));
          eChartData.value.routeName.push(item.routeName);
          index = eChartData.value.dis.indexOf(Number(item.distance));
          routeId.value = item.routeId;
          routeName.value = item.routeName;
          marker = new AMap.Text({
            zIndex: 1000,
            position: new AMap.LngLat(
              event.lnglat.getLng(),
              event.lnglat.getLat()
            ),
            text: index.toString(),
            anchor: "center",
            style: {
              color: "#000",
              "background-color": "red",
            },
          });
          marker.dis = item.distance;
          markers.push(marker);
          map.add(marker);
        } else {
          if (index !== -1) {
            eChartData.value.dis.splice(index, 1);
            eChartData.value.wei.splice(index, 1);
            eChartData.value.time.splice(index, 1);
            eChartData.value.routeName.splice(index, 1);
            routeId.value = undefined;
          }
          if (markers.indexOf(marker) !== -1) {
            markers.splice(markers.indexOf(marker), 1);
            map.remove(marker);
            renderMarkers();
          }
        }
        isActive = !isActive;
      });
      // 点击事件结束
      map.add(polygon);
      watch(mapRouteName, () => {
        if (mapRouteName.value == item.routeName) {
          polygon.setOptions(activePolyOption);
          map.setZoomAndCenter(10, polygonPath[0]);
        } else {
          polygon.setOptions(unchosenPolyOption);
        }
      });

      watch(routeIndex, () => {
        if (
          routeIndex.value &&
          eChartData.value.groupRouteName[routeIndex.value] == item.routeName
        ) {
          polygon.setOptions(activePolyOption);
          map.setZoomAndCenter(11, polygonPath[0]);
        } else {
          polygon.setOptions(unchosenPolyOption);
        }
      });
      watch(clickRoute, () => {
        if (clickRoute.value == item.routeName) {
          polygon.setOptions(activePolyOption);
        } else {
          polygon.setOptions(unchosenPolyOption);
        }
      });
      // 重新渲染 marker
      function renderMarkers() {
        markers.forEach((marker) => {
          const newIndex = eChartData.value.dis.indexOf(Number(marker.dis));
          if (marker.getText() !== newIndex.toString()) {
            marker.setText(newIndex.toString());
          }
        });
      }
      // //路线绘制
    });
  }
  const loadCalculate = ref<boolean>(false);
  const CalculateBtnFunction = () => {
    loadCalculate.value = true;
    if (area.value == "韶关市") {
      clusterStore
        .calculateAllAction({ apiKey: pathCalculateInfo.value.apiKey })
        .then(() => {
          choiceCalculateType.value = 1;
          loadCalculate.value = false;
        });
    } else {
      pathCalculateInfo.value.assignNumber = num.value;
      pathCalculateInfo.value.areaName = area.value;
      clusterStore.pathCalculateOneAction(pathCalculateInfo.value).then(() => {
        loadCalculate.value = false;
        choiceCalculateType.value = 2;
      });
    }
  };
  const isResultPointsFinished = ref<boolean>(true);
  //获取路线详情-大区路线聚集区信息
  clusterStore.getRouteDetailsAction().then(() => {
    isResultPointsFinished.value = false;
  });
  //定义弹框标题accumulationName的变量
  const titleAccumulationName = ref<string>("");
  //获取路线详情-聚集区下商户信息
  const accumulationNameClick = (accumulation: any) => {
    console.log(accumulation);

    titleAccumulationName.value = accumulation.routeName;
    clusterStore.getStoreDetailsAction(accumulation.routeId);
    isOpenRouteDialog.value = true;
    if (mapRouteName.value != accumulation.routeName) {
      mapRouteName.value = accumulation.routeName;
    }
  };
  //定义是否打开弹窗的变量
  const isOpenRouteDialog = ref(false);
  //关闭窗口
  const closeRouteDialog = () => {
    isOpenRouteDialog.value = false;
  };
  const saveLoading = ref<boolean>(false);
  const addRouteBtn = async () => {
    saveLoading.value = true;
    if (adjustSave.value) {
      clusterStore.convex.forEach((item: any) => {
        delete item.createTime;
        delete item.updateTime;
        delete item.versionId;
        delete item.delete;
        delete item.routeId;
        delete item.workTime;
      });
      let saveRouteData = clusterStore.convex!.map((i: any) => {
        i.areaId = Number(i.areaId);
        i.transitDepotId = Number(i.transitDepotId);
        return i;
      });
      // 重新计算情况数据
      eChartData.value = {
        dis: [],
        wei: [],
        time: [],
        routeName: [],
        groupDis: [],
        groupTime: [],
        groupWei: [],
        groupRouteName: [],
      };
      markers.splice(0, markers.length);
      ElMessage.closeAll("warning");
      clusterStore.postAddRouteAction(saveRouteData).then(() => {
        saveLoading.value = false;
        if (clusterStore.saveState) {
          refreshConvex();
        }
      });
    }
    if (choiceCalculateType.value == 0 && !adjustSave.value) {
      saveLoading.value = false;
      ElMessage.warning("请先进行重新计算");
    }
    await saveRoute();
  };

  async function saveRoute() {
    if (choiceCalculateType.value == 1) {
      clusterStore.newPathResultAll?.forEach((item) => {
        delete item.createTime;
        delete item.updateTime;
        delete item.versionId;
        delete item.delete;
        delete item.routeId;
        delete item.workTime;
      });
      let saveRouteData = clusterStore.newPathResultAll!.map((i) => {
        i.areaId = Number(i.areaId);
        i.transitDepotId = Number(i.transitDepotId);
        return i;
      });
      // console.log(saveRouteData);
      clusterStore.postAddRouteAction(saveRouteData).then((res) => {
        saveLoading.value = false;
        if (clusterStore.saveState) {
          refreshConvex();
        }
        return res;
      });
    }

    if (choiceCalculateType.value == 2) {
      clusterStore.newPathResult?.forEach((item) => {
        delete item.createTime;
        delete item.updateTime;
        delete item.versionId;
        delete item.delete;
        delete item.routeId;
        delete item.workTime;
      });
      let saveRouteData = clusterStore.newPathResult!.map((i) => {
        i.areaId = Number(i.areaId);
        i.transitDepotId = Number(i.transitDepotId);
        return i;
      });
      // console.log(saveRouteData);
      clusterStore.postAddRouteAction(saveRouteData).then((res) => {
        saveLoading.value = false;
        if (clusterStore.saveState) {
          refreshConvex();
        }
        return res;
      });
    }
  }

  function exportMessage() {
    const load = ElLoading.service({
      lock: true,
      text: "导出路径中...",
      background: "rgba(0, 0, 0, 0.7)",
    });
    clusterStore.exportRouteMessage().then((res: any) => {
      // 创建一个 URL 对象链接到 Blob
      const url = URL.createObjectURL(res);
      // 创建一个 <a> 元素并设置属性
      const link = document.createElement("a");
      link.href = url;
      link.download = "路线详细.xlsx"; // 设置下载文件名
      // 将 <a> 元素添加到文档并触发点击
      document.body.appendChild(link);
      link.click();
      // 移除 <a> 元素并释放 URL 对象
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      load.close();
    });
  }

  //  调整打卡点
  const adjustConfirm = ref<boolean>(false);
  const adjustLoad = ref<boolean>(false);
  const adjustText = ref<string>("打卡点调整");
  const adjustSave = ref<boolean>(false);
  function adjustPoint() {
    if (!points.value) {
      ElMessage.warning("请等待打卡点数据加载完毕");
    } else if (!adjustConfirm.value) {
      adjustLoad.value = true;
      ElMessage({
        message: "已进入调整打卡点模式，请选择要调整的打卡点",
        type: "warning",
        duration: 0,
      });
      watch(accumulationId, (newValue) => {
        ElMessage.closeAll("warning");
        ElMessage({
          message: `已选择最新的打卡点，打卡点的Id为${newValue},然后请选择打卡点的新路径`,
          duration: 0,
          type: "warning",
        });

        watch(routeId, (newValue) => {
          if (newValue) {
            ElMessage.closeAll("warning");
            ElMessage({
              message: `已选择最新的路径，路径的名称为${routeName.value},请再点击调整按钮确认`,
              duration: 0,
              type: "warning",
            });

            adjustLoad.value = false;
            adjustText.value = "点击确认调整";
            adjustConfirm.value = true;
          }
        });
      });
    } else if (adjustConfirm.value) {
      if (routeId.value && accumulationId.value) {
        clusterStore
          .adjustPointAction({
            routeId: routeId.value,
            accumulationId: accumulationId.value,
          })
          .then(() => {
            adjustConfirm.value = false;
            ElMessage.closeAll("warning");
            const loading = ElLoading.service({
              lock: true,
              text: "加载地图数据中...",
              background: "rgba(0, 0, 0, 0.7)",
            });
            clusterStore
              .calculateSingleRouteAction({
                apiKey: pathCalculateInfo.value.apiKey,
                routeName1: oldRouteName.value,
                routeName2: routeName.value,
              })
              .then(() => {
                for (let item of clusterStore.SingleRoute) {
                  let index = clusterStore.convex.findIndex(
                    (obj: any) => obj.routeName === item.routeName
                  );
                  if (index !== -1) {
                    clusterStore.convex[index] = item;
                  }
                }
                const Overlays = map.getAllOverlays("polygon");
                map.remove(Overlays);
                countPathResult();
                loading.close();
                ElMessage({
                  message:
                    "调整结果如图，如需保存请点击保存路径按钮，否则刷新页面恢复调整前状态",
                  duration: 0,
                  type: "warning",
                });
                adjustSave.value = true;
              });
          });
      } else {
        ElMessage({ message: `请选择打卡点的新路径`, type: "warning" });
      }
    }
  }

  function refreshConvex() {
    const loading = ElLoading.service({
      lock: true,
      text: "加载地图数据中...",
      background: "rgba(0, 0, 0, 0.7)",
    });
    clusterStore.getConvexAction(pathCalculateInfo.value.apiKey).then(() => {
      clusterStore.getColorConvexAction().then(() => {
        countPathResult();
        loading.close();
      });
    });
  }
</script>

<style lang="less" scoped>
  .position-message {
    position: absolute;
    top: 10px;
    left: 10px;
    bottom: auto;
    color: white;
    z-index: 999;
    opacity: 0.7;
    font-size: 10px;
    line-height: 15px;
    background-color: rgb(87, 83, 83);
  }
  .route {
    width: 100%;
    display: flex;
    .el-button {
      font-size: 14px;
    }

    .map {
      position: relative;
      flex-grow: 1;
      margin: 0 1.5vw;
      :deep(.amap-marker-label) {
        background-color: #3490f5;
        border: 0px;
        border-radius: 30%;
        position: relative;
      }
      #container {
        position: relative;
        padding: 0px;
        margin: 0px;
        width: 100%;
        height: 103%;
        margin: 0.5vh 0;
      }
      .btn-box {
        display: flex;
        width: 100%;
        align-items: center;
        justify-content: space-between;

        :deep(.el-select) {
          width: 16vw;
        }

        :deep(.el-input) {
          width: 100%;
        }

        .num {
          width: 120px !important;
        }

        :deep(.el-input-number .el-input) {
          border: none !important;
        }

        .btnsbox {
          display: flex;
        }
      }

      .icon {
        position: absolute;
        top: 6px;
        right: 5px;
        z-index: 10;
        width: 20px;
        height: 25px;
        box-shadow: 1px 1px 1px 1px rgb(0, 0, 0, 0.4);
        border-radius: 10%;
        background-color: #97c7e7;
      }
    }
    .aside {
      width: 20vw;
      transition: width 0.3s ease;
    }
    .expanded {
      width: 3vw;
    }
    .slide-fade-enter-active {
      transition: opacity 0.8s;
    }
    .slide-fade-enter-from {
      opacity: 0;
    }

    .dv-border-box-9 {
      height: 80vh;
      width: 20vw;
      box-shadow: 10px 10px 5px 5px rgb(0, 0, 0, 0.4);
      .arrow {
        position: absolute;
        right: -8%;
        top: 26%;
        width: 2.5vw;
        height: 6vh;
        background-color: #001731;
        cursor: pointer;
      }
      :deep(.el-input__wrapper) {
        font-size: 18px;
        width: 10vw;
        background-color: #001731;
        box-shadow: none;
      }

      :deep(.el-input__inner) {
        font-weight: bolder;
        text-align: center;
      }
      .el-select {
        --el-select-input-focus-border-color: transparent;
        position: absolute;
        left: 50%;
        transform: translate(-50%, -30%);
      }

      :deep(.el-icon) {
        font-size: 20px;
      }

      .el-input {
        border: none;
      }
      .rtitle {
        width: 140px;
        height: 50px;
        font-size: 20px;
        letter-spacing: 2px;
        line-height: 40px;
        background-color: #001731;
        text-align: center;
        color: rgb(204, 255, 255);
        font-weight: bolder;
        position: absolute;
        left: 50%;
        transform: translate(-50%, -30%);
      }
      .num {
        width: 120px;
        height: 40px;
      }

      .el-input__inner {
        color: #ff0000;
      }

      .leftInformation {
        width: 100%;
        height: 100%;
        .left-container {
          width: 100%;
          height: 100%;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          .left-title {
            margin-top: 2vh;
            margin-bottom: 2vh;
            text-align: center;
            & > p:nth-child(1) {
              font-size: 20px;
              font-weight: bolder;
              color: #c9f4fb;
            }

            & > p:nth-child(2) {
              margin-top: 1vh;
              font-size: 16px;
              color: #c9f4fb;
            }
          }
          .leftitem {
            width: 100%;
            height: auto;
            display: flex;
            flex-direction: column;
          }
        }

        .button {
          display: flex;
          justify-content: center;
          margin-bottom: 2vh;
          .el-button {
            width: 8vw;
          }
        }
      }

      .rightInformation {
        margin-right: -1vw;
        .detailedRoute {
          padding-left: 1vw;
          padding-top: 4vh;
          width: 85%;
          .accumulationNameLi::before {
            content: "";
            display: inline-block;
            width: 8px;
            height: 8px;
            background-color: #90ade8;
            border-radius: 50%;
            margin-bottom: 4px;
            margin-right: 0.5vw;
          }
          .el-collapse {
            --el-collapse-header-bg-color: rgb(2, 119, 168);
            --el-collapse-header-text-color: #003766;
            --el-collapse-header-font-size: 10px;
            --el-collapse-content-bg-color: #001731;
            --el-collapse-content-font-size: 10px;
            --el-collapse-content-text-color: #a4c5e5;
            --el-collapse-border-color: #001731;
            :deep(.el-loading-spinner) {
              margin-top: 28vh;
            }
          }

          .el-collapse-item {
            position: relative;
            ::v-deep(.el-collapse-item__content) {
              // background-color: #a4c5e5;
              padding: 0;
            }
            ::v-deep(.el-collapse-item__header) {
              font-size: 16px;
              background-color: #97c7e7;
              line-height: normal;
            }
          }

          .el-collapse-item:before {
            position: absolute;
            top: 0;
            border-right: 25px solid #97c7e7;
            border-top: 24px solid transparent;
            border-bottom: 24px solid transparent;
            content: "";
          }
        }

        .saveRoute {
          width: 120px;
          height: 40px;
          position: absolute;
          left: 50%;
          transform: translate(-50%, 0);
          bottom: 3%;
        }
      }
    }
  }
  .active {
    background-color: rgb(2, 119, 168);
    padding: 0 0.5vw;
  }
  .el-select-dropdown__item {
    display: grid !important;
    place-items: center !important;
    font-size: 18px;
    font-weight: bolder;
  }
  .el-popper .el-popper__arrow::before {
    border-top: 1px solid #97c7e7;
    background-color: #97c7e7 !important;
  }
  // :deep(.el-collapse) {
  //   font-size: 16px;
  // }
</style>
