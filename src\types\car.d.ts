//添加车辆
export interface ICarAddData {
    carDriverName: string,
    carDriverPhone: string,
    deliveryAreaName: string,
    licensePlateNumber: string,
    maxLoad: string,
    status: string,
}

//删除车辆
export interface ICarDeleteData {
    carId: number,
}

//修改车辆
export interface ICarUpdateData {
    carDriverName: string,
    carDriverPhone: string,
    carId: number,
    deliveryAreaName: string,
    licensePlateNumber: string,
    maxLoad: string,
    status: string
}

//获取车辆
export interface ICarGetData {
    carDriver?: string,
    licensePlateNumber?: string,
    maxLoad?: string,
    pageNum: number,
    pageSize: number, 
    status?: string,
    teamName?: string,
}

//车辆信息
export interface ICarData {
    userName: string,
    phone: string,
    carId: number,
    licensePlateNumber: string,
    maxLoad: string,
    status: string,
    teamName: string,
}

//添加车辆实情
export interface IAcutualAddCar {
    actualLoad: string,
    actualTime: string,
    carDriverName: string,
    date: string,
    deliveryAreaName: string,
    licensePlateNumber: string,
    route: string,
    week: string
}

//删除车辆实情
export interface IAcutualDeleteCar {
    carId: number,
}

//修改车辆实情
export interface IAcutualUpdateCar {
    actualLoad: string,
    actualTime: string,
    carDriverName: string,
    carId: number,
    date: string,
    deliveryAreaName: string,
    licensePlateNumber: string,
    route: string,
    week: string
}

//获得车辆实情
export interface IAcutualGetCar {
    pageNum: number,
    pageSize: number
}

//导入日志
export interface ILogImport {
    pageNum: number,
    pageSize: number,
    type: string
}

//删除日志
export interface ILogDelete {
    logsId: number
}

//下载日志
export interface ILogDownload {
    fileName: string,
    importTime: string
}


//下载空白表格
export interface IFromDownload {
    code: number,
    csvOrExcel: number
}
