-- 插入权限控制所需的权限点数据
-- 如果权限点不存在则插入，存在则跳过

-- 用户服务相关权限点
INSERT IGNORE INTO operation (operation_name, operation_state) VALUES 
('user-service:password:update', '修改密码权限'),
('user-service:user:add', '添加用户权限'),
('user-service:user:update', '修改用户信息权限'),
('user-service:role:set', '设置角色权限');

-- 查询插入的权限点ID，为系统管理员角色自动分配这些权限
-- 注意：这里假设系统管理员角色ID为1，如果不是请根据实际情况修改

-- 为系统管理员角色分配新增的权限点
INSERT IGNORE INTO role_operation (role_id, operation_id, status)
SELECT 1, operation_id, '1'
FROM operation 
WHERE operation_name IN (
    'user-service:password:update',
    'user-service:user:add', 
    'user-service:user:update',
    'user-service:role:set'
);

-- 查看插入结果
SELECT * FROM operation WHERE operation_name LIKE 'user-service:%';
