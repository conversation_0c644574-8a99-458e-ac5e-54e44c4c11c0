import requests from "../index";
import type {
  IAddLocation,
  IGetLocationList,
  IUpdateLocation,
  ILogLocationImport,
  IFromDownload,
  IEditLocation,
  IDeleteLog,
  ILogDownload,
} from "@/types/pos";
import { IRequest } from "../request/type";

//导出选址信息
export function exportLocation() {
  return requests.get<IRequest<any>>({
    url: "/pickup/pickupLocation/pickupLocationExport",
    responseType: "blob",
  });
}

//导入取货户信息bro
export function importLocation(formData: FormData, config: any) {
  return requests.post<IRequest<any>>({
    url: "/pickup/pickupLocation/pickupLocationImport",
    data: formData,
    headers: {
      accept: "*/*",
      "Content-Type": "multipart/form-data",
    },
    onUploadProgress: config.onUploadProgress,
    signal: config.signal,
  });
}

//导入日志
export function importLocationLog(params: any) {
  return requests.get<IRequest<ILogLocationImport>>({
    url: "/datamanagement/getImportLogs",
    params,
  });
}

//获取选址列表
export function getLocationList(params: IGetLocationList) {
  return requests.get<IRequest<IGetLocationList>>({
    url: "/pickup/pickupLocation/pickupLocationList",
    params,
  });
}

//修改选址信息
export function updateLocation(params: IUpdateLocation) {
  return requests.put<IRequest<IUpdateLocation>>({
    url: "/pickup/pickupLocation/pickupLocationUpdate",
    params,
    paramsSerializer: (params) => new URLSearchParams(params).toString(),
  });
}

//添加选址信息
export function addLocation(params: IAddLocation) {
  return requests.post<IRequest<IAddLocation>>({
    url: "/pickup/pickupLocation/pickupLocationAdd",
    params,
  });
}

//下载日志
export function downloadLog(params: ILogDownload) {
  return requests.get<IRequest<ILogDownload>>({
    url: "/datamanagement/downloadLogs",
    headers: { "Content-Type": "application/x-download" },
    responseType: "blob",
    params,
  });
}

//删除日志
export function deleteLog(params: IDeleteLog) {
  return requests.delete<IRequest<IDeleteLog>>({
    url: "/datamanagement/deleteImportLogs",
    params,
  });
}

//下载null表格
export function downloadTemplate(params: IFromDownload) {
  return requests.post<IRequest<IFromDownload>>({
    url: "/pickup/pickupUser/downloadNullFrom",
    headers: { "Content-Type": "application/x-download" },
    responseType: "blob",
    params,
  });
}

//重新计算
export function accurate() {
  return requests.patch<IRequest<any>>({
    url: "/pickup/pickupLocation/recalculate",
  });
}

//编辑
export function editLocation(params: IEditLocation) {
  return requests.patch<IRequest<IEditLocation>>({
    url: "/pickup/pickupLocation/recalculate",
    params,
    paramsSerializer: (params) => new URLSearchParams(params).toString(),
  });
}

//待分配列表
export function getUnassignedList() {
  return requests.get<IRequest<any>>({
    url: "/pickup/pickupLocation/toBeaSsignedList",
  });
}
