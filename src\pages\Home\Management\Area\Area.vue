<template>
  <div class="route">
    <div class="map">
      <div id="container"></div>
      <div class="position-message" v-if="appearPositionMessage">
        <template
          v-for="(item, index) in clusterStore.storeResult"
          :key="index"
        >
          <div>{{ index + 1 }} : {{ item.accumulationAddress }}</div>
        </template>
      </div>
    </div>
    <div class="data" style="width: 60%">
      <BorderBox9 :color="['#90ade8', '#90ade8']">
        <div class="input">
          <el-input
            style="width: 440px"
            placeholder="请点击搜索"
            @click="openSearchAdd"
          />
          <div class="searchContent" v-if="searchOpen">
            <div class="closeBold" @click="closeSearchAdd">x</div>
            <div class="content">
              <div class="group">
                <el-form
                  label-width="auto"
                  :model="searchFromData"
                  ref="searchModal"
                >
                  <el-form-item label="班组" prop="searchFormTeam">
                    <el-select
                      style="width: 300px"
                      placeholder="请选择"
                      v-model="searchFromData.searchFormTeam"
                    >
                      <el-option
                        v-for="item in store.searchTeamInfo.teamList"
                        :label="item"
                        :value="item"
                        :key="item + '2'"
                      />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="配送域" prop="searchFormDelivery">
                    <el-select
                      style="width: 300px"
                      placeholder="请选择"
                      v-model="searchFromData.searchFormDelivery"
                    >
                      <el-option
                        v-for="item in store.searchTeamInfo.deliveryList"
                        :label="item"
                        :value="item"
                        :key="item + '2'"
                      />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="中转站" prop="searchFormTransitDepot">
                    <el-select
                      style="width: 300px"
                      placeholder="请选择"
                      v-model="searchFromData.searchFormTransitDepot"
                    >
                      <el-option
                        v-for="item in store.searchTeamInfo.transitDepotList"
                        :label="item"
                        :value="item"
                        :key="item + '2'"
                      />
                    </el-select>
                  </el-form-item>
                </el-form>

                <div class="btns">
                  <el-button @click="resetSearch">清空</el-button>
                  <el-button style="margin-left: 40px" @click="confirmSearch"
                    >搜索</el-button
                  >
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="btns">
          <el-button
            :icon="Plus"
            class="btn"
            @click="openAdd"
            v-if="hasOp('data-management:team:add')"
            >添加班组</el-button
          >
          <el-button
            :icon="Delete"
            class="btn"
            style="margin-left: 20px"
            @click="openDelete"
            v-if="hasOp('data-management:team:delete')"
            >删除班组</el-button
          >
          <el-button
            :icon="EditPen"
            class="btn"
            @click="openChange"
            style="margin-left: 20px"
            v-if="hasOp('data-management:team:update')"
            >修改信息</el-button
          >
        </div>
        <div class="table" v-if="hasOp('data-management:team:view')">
          <el-table
            ref="tableRef"
            :cell-style="{ textAlign: 'center' }"
            :header-cell-style="{
              height: '4vh',
              'text-align': 'center',
            }"
            size="small"
            :row-style="{ height: '3.9vh' }"
            style="font-size: 0.8vw"
            :data="areaList"
            @selection-change="handleSelect"
          >
            <el-table-column type="selection" min-width="2%" />
            <el-table-column
              label="序号"
              min-width="2%"
              type="index"
              :index="Nindex"
            />
            <el-table-column label="班组名称" min-width="3%" prop="teamName" />
            <el-table-column
              label="配送域"
              min-width="4%"
              prop="deliveryAreaName"
            >
              <template #default="scope">
                <div>
                  {{
                    scope.row.deliveryAreaName
                      ? scope.row.deliveryAreaName
                      : "无"
                  }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              label="中转站"
              min-width="4%"
              prop="transitDepotName"
            >
              <template #default="scope">
                <div>
                  {{
                    scope.row.transitDepotName
                      ? scope.row.transitDepotName
                      : "无"
                  }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="车辆总数" min-width="4%" prop="carSum" />
            <el-table-column label="路径总数" min-width="4%" prop="routeSum" />
          </el-table>
        </div>
      </BorderBox9>
      <el-pagination
        v-if="!inSearch"
        class="pagi"
        layout="prev, pager, next"
        v-model:current-page="searchData.pageNum"
        v-model:page-size="searchData.pageSize"
        :total="store.areaTotalList.length"
        @current-change="handlePageChange"
      />
      <el-pagination
        v-if="inSearch"
        class="pagi"
        layout="prev, pager, next"
        :current-page="searchData.pageNum"
        :page-size="searchData.pageSize"
        :total="areaList.length"
        @current-change="handlePageChange"
      />
      <div class="areaDialog">
        <addArea
          ref="addAreaRef"
          :teamName="addTeamName"
          @confirmAdd="confirmAdd"
        />
        <deleteArea
          ref="deleteAreaRef"
          @confirmDelete="confirmDelete"
        ></deleteArea>
        <changeArea
          v-if="info"
          ref="changeAreaRef"
          @confirmChange="confirmChange"
          :teamName="teamName"
          :teamId="teamId"
          :info="info"
        />
        <pathDialog ref="pathRef" dType="班组"></pathDialog>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { MAP_KEY, SECURITY_CODE } from "@/utils/getMapKey";
  import { modifyUserAgent } from "@/utils/modifyUserAgent";
  import pathDialog from "../Path/pathDialog.vue";
  import { hasOp } from "@/op";
  import { Plus, Delete, EditPen } from "@element-plus/icons-vue";
  import { BorderBox9 } from "@dataview/datav-vue3";
  import AMapLoader from "@amap/amap-jsapi-loader";
  import { useClusterStore } from "@/store/cluster";
  import { data1, data2, data3, data4, data5 } from "./data/data";
  import addArea from "./addArea/addArea.vue";
  import changeArea from "./changeArea/changeArea.vue";
  import deleteArea from "./deleteArea/deleteArea.vue";
  import { useAreaStore } from "@/store/managerment/area";
  import { useDeliveryStore } from "@/store/delivery";

  const deliveryStore = useDeliveryStore();
  modifyUserAgent();

  window._AMapSecurityConfig = {
    securityJsCode: SECURITY_CODE,
  };

  //聚集区Store
  const clusterStore = useClusterStore();
  var map: any = null;
  AMapLoader.load({
    key: MAP_KEY, // 申请好的Web端开发者Key，首次调用 load 时必填
    version: "2.0", // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
    plugins: ["AMap.DistrictSearch", "AMap.MarkerCluster"], // 需要使用的的插件列表，如比例尺'AMap.Scale'等 // 需要使用的的插件列表，如比例尺'AMap.Scale'等
  }).then(async (AMap: any) => {
    // 创建一个DistrictSearch对象，用于搜索行政区划信息。
    const district = new AMap.DistrictSearch({
      subdistrict: 1,
      extensions: "all",
      level: "province",
    });
    // 使用DistrictSearch对象搜索"韶关市"的行政区划信息，并在回调函数中处理结果
    district.search("韶关市", async function (_: any, result: any) {
      const bounds = result.districtList[0].boundaries;
      const mask = [];
      for (let i = 0; i < bounds.length; i++) {
        mask.push([bounds[i]]);
      }

      map = new AMap.Map("container", {
        // 设置地图容器id
        mask: mask, // 为Map实例制定掩模的路径,各图层将值显示路径范围内图像,3D模式下有效
        zoom: 9, // 设置当前显示级别
        expandZoomRange: true, // 开启显示范围设置
        zooms: [7, 8.5], //最小显示级别为7，最大显示级别为20
        center: [113.767587, 24.718014], // 设置地图中心点位置
        viewMode: "3D", // 特别注意,设置为3D则其他地区不显示
        zoomEnable: true, // 是否可以缩放地图
        resizeEnable: true,
      });
      //分割线涂色
      let polygon: any[] = [
        "polygon1",
        "polygon2",
        "polygon3",
        "polygon4",
        "polygon5",
      ];
      // 各班组颜色
      let colorArr = ["#f6efa6", "#b3d6e9", "#f5dbdc", "#99ccbd", "#cb9ebd"];
      let polygonData: any = [data1, data2, data3, data4, data5];
      let colorMapArr = ["#d79d46", "#6880a4", "#a9686b", "#5eaa91", "#a75b8f"];
      for (let i = 0; i < polygon.length; i++) {
        polygon[i] = new AMap.Polygon({
          path: polygonData[i],
          fillOpacity: 0.7,
          fillColor: colorArr[i],
          strokeWeight: 4,
          strokeColor: colorMapArr[i],
        });
      }
      map.add(polygon);

      // 添加文字标注
      const fontStyle2 = {
        "margin-bottom": "20rem",
        "text-align": "center",
        "font-size": "20px",
        color: "black",
        "font-weight": "800",
      };
      const fontStyle3 = {
        "margin-bottom": "20rem",
        "text-align": "center",
        "font-size": "15px",
        color: "black",
      };
      const text11 = new AMap.Text({
        text: "班组四",
        position: new AMap.LngLat(113.147669, 25.329892),
        style: fontStyle2,
      });
      const text12 = new AMap.Text({
        text: "班组二",
        position: new AMap.LngLat(113.075875, 24.915856),
        style: fontStyle2,
      });
      const text13 = new AMap.Text({
        text: "班组一",
        position: new AMap.LngLat(113.569175, 25.215764),
        style: fontStyle2,
      });
      const text14 = new AMap.Text({
        text: "班组五",
        position: new AMap.LngLat(113.949986, 24.250581),
        style: fontStyle2,
      });
      const text15 = new AMap.Text({
        text: "班组三",
        position: new AMap.LngLat(114.01184, 25.117653),
        style: fontStyle2,
      });

      const text16 = new AMap.Text({
        text: "新丰县中转站",
        position: new AMap.LngLat(114.19539, 24.05113),
        style: fontStyle3,
      });

      const text17 = new AMap.Text({
        text: "坪石镇中转站",
        position: new AMap.LngLat(113.0205, 25.2),
        style: fontStyle3,
      });
      const text18 = new AMap.Text({
        text: "翁源县中转站",
        position: new AMap.LngLat(114.14502, 24.33734),
        style: fontStyle3,
      });
      const text19 = new AMap.Text({
        text: "马市烟叶中转站",
        position: new AMap.LngLat(114.154214, 25.025656),
        style: fontStyle3,
      });

      const text20 = new AMap.Text({
        text: "班组一物流配送中心",
        position: new AMap.LngLat(113.48208, 24.964151),
        style: fontStyle3,
      });

      const text21 = new AMap.Text({
        text: "班组二物流配送中心",
        position: new AMap.LngLat(112.98208, 24.754151),
        style: fontStyle3,
      });

      map.add([
        text11,
        text12,
        text13,
        text14,
        text15,
        text16,
        text17,
        text18,
        text19,
        text20,
        text21,
      ]);
    });
  });

  onMounted(() => {
    store.getAreaTotalData(searchData.pageSize).then(() => {
      getTableInitData(searchData.pageSize);
    });
  });

  const searchFromData = ref<any>({
    searchFormTeam: "",
    searchFormDelivery: "",
    searchFormTransitDepot: "",
  });
  const pathRef = ref<any>();
  const searchResult = ref<any>([]);
  const inSearch = ref<boolean>(false);
  const searchOpen = ref<boolean>(false);

  function openSearchAdd() {
    store.searchAreaData().then(() => {
      searchOpen.value = true;
    });
  }
  function closeSearchAdd() {
    searchOpen.value = false;
  }

  function resetSearch() {
    searchFromData.value = {
      searchFormTeam: "",
      searchFormDelivery: "",
      searchFormTransitDepot: "",
    };
  }
  function confirmSearch() {
    if (
      !searchFromData.value.searchFormTeam &&
      !searchFromData.value.searchFormDelivery &&
      !searchFromData.value.searchFormTransitDepot
    ) {
      searchResult.value = [];
      store.getAreaTotalData(searchData.pageSize).then(() => {
        getTableInitData(searchData.pageSize);
      });
      inSearch.value = false;
      return;
    }
    searchResult.value = search(store.areaTotalList, searchFromData.value);
    searchData.pageNum = 1;
    areaList.value = searchResult.value.slice(
      searchData.pageNum * searchData.pageSize - searchData.pageSize,
      searchData.pageNum * searchData.pageSize
    );
    inSearch.value = true;
  }

  function search(data: any, searchParams: any) {
    return data.filter((item: any) => {
      // 根据搜索条件进行匹配
      const teamName = searchParams.searchFormTeam
        ? item.teamName === searchParams.searchFormTeam
        : true;
      let matchesDelivery = true;
      if (searchParams.searchFormDelivery) {
        matchesDelivery =
          item.deliveryAreaName &&
          item.deliveryAreaName.includes(searchParams.searchFormDelivery);
      }
      let matchesTransitDepot = true;
      if (searchParams.searchFormTransitDepot) {
        matchesTransitDepot =
          item.transitDepotName &&
          item.transitDepotName.includes(searchParams.searchFormTransitDepot);
      }
      // let matchesDelivery = true
      return teamName && matchesDelivery && matchesTransitDepot;
    });
  }

  const tableRef = ref<any>();
  const addAreaRef = ref<InstanceType<typeof addArea>>();
  const changeAreaRef = ref<any>();
  const deleteAreaRef = ref<any>();
  const addTeamName = ref<string>("");
  const searchModal = ref<any>();
  //打开添加弹窗
  function openAdd() {
    if (inSearch.value) {
      ElMessage({
        message: "还在搜索状态中,禁止打开弹窗",
        type: "warning",
      });
      return;
    }
    store.getAddTeamInfo().then(() => {
      addTeamName.value = store.addTeamInfo.teamName;
      deliveryStore.getDeliverySelectList().then(() => {
        typeof addAreaRef.value?.addAreaOpen === "boolean"
          ? (addAreaRef.value.addAreaOpen = true)
          : false;
      });
    });
  }

  function openDelete() {
    if (inSearch.value) {
      ElMessage({
        message: "还在搜索状态中,禁止打开弹窗",
        type: "warning",
      });
      return;
    }
    if (tableRef.value.getSelectionRows().length != 1) {
      ElMessage({
        message: "请单选!",
        type: "warning",
      });
      return;
    }
    deleteAreaRef.value.deleteVis = true;
  }
  //确定添加班组的信息
  function confirmAdd(pathVis: boolean) {
    store.getAreaTotalData(searchData.pageSize).then(() => {
      pageQuery(searchData.pageNum, searchData.pageSize);
      ElMessage({
        message: "添加成功!",
        type: "success",
      });
      if (!pathVis) {
        pathRef.value.vis = true;
      }
      // updateMap()
    });
  }

  //确定删除
  function confirmDelete() {
    deleteAreaRef.value.deleteVis = false;
    if (tableRef.value.getSelectionRows()[0].deliveryAreaName) {
      ElMessage({
        message: "请勾选配送域为无的班组",
        type: "warning",
      });
      return;
    }
    tableRef.value.getSelectionRows().forEach((item: any) => {
      store.deleteAreaData({ teamId: item.teamId }).then((res) => {
        if (res.code === 50001 || res.message === "系统异常") {
          ElMessage.error("系统异常!");
          return;
        }
        store.getAreaTotalData(searchData.pageSize).then(() => {
          pageQuery(searchData.pageNum, searchData.pageSize);
          ElMessage({
            message: "删除成功!",
            type: "success",
          });
          // updateMap()
        });
      });
    });
  }
  const teamId = ref<any>();
  const teamName = ref<any>();
  // 打开修改弹窗
  function openChange() {
    if (tableRef.value.getSelectionRows().length !== 1) {
      ElMessage({
        message: "请单选一个班组修改",
        type: "warning",
      });
      return;
    }
    deliveryStore.getDeliverySelectList().then(() => {
      typeof changeAreaRef.value?.changeAreaOpen === "boolean"
        ? (changeAreaRef.value.changeAreaOpen = true)
        : false;
    });
  }

  //确认修改弹窗
  function confirmChange() {
    current.value = searchData.pageNum;
    store.getAreaTotalData(searchData.pageSize).then(() => {
      searchData.pageNum = current.value;
      pageQuery(searchData.pageNum, searchData.pageSize);
      ElMessage({
        message: "修改成功!",
        type: "success",
      });
      pathRef.value.vis = true;
      // updateMap()
    });
  }

  const info = ref<any>("");
  function handleSelect(rowData: any) {
    if (rowData.length >= 1) {
      teamId.value = rowData[0].teamId;
      teamName.value = rowData[0].teamName;
      info.value = Object.assign({}, rowData[0]);
      if (!rowData[0].deliveryAreaName) {
        info.value.deliveryAreaName = "无";
      }
      if (!rowData[0].transitDepotName) {
        info.value.transitDepotName = "";
      }
    }
  }

  //仓库
  const store = useAreaStore();
  //仓库内的数据
  const areaList = ref<any>([]);
  //查询对象
  const searchData = reactive({
    pageNum: 1,
    pageSize: 10,
  });

  const current = ref<number>(0);
  //处理分页
  async function handlePageChange(num: number) {
    searchData.pageNum = num;
    if (inSearch.value) {
      areaList.value = searchResult.value.slice(
        searchData.pageNum * searchData.pageSize - searchData.pageSize,
        searchData.pageNum * searchData.pageSize
      );
      return;
    }
    pageQuery(searchData.pageNum, searchData.pageSize);
  }

  //传入分页参数
  function pageQuery(pageNum: number, pageSize: number) {
    if (inSearch.value) {
      confirmSearch();
      return;
    }
    //当前页数
    areaList.value = store.areaTotalList.slice(
      pageNum * pageSize - pageSize,
      pageNum * pageSize
    );
  }

  //获取初始表格数据
  function getTableInitData(pageSize: number) {
    areaList.value = store.areaTotalList.slice(0, pageSize);
  }

  function Nindex(index: number) {
    const page = searchData.pageNum; // 当前页码
    const pagesize = searchData.pageSize; // 每页条数
    return index + 1 + (page - 1) * pagesize;
  }
</script>

<style lang="scss" scoped>
  .position-message {
    position: absolute;
    top: 10px;
    left: 10px;
    bottom: auto;
    color: white;
    opacity: 0.7;
    font-size: 10px;
    line-height: 15px;
    background-color: rgb(87, 83, 83);
  }

  .route {
    width: 100%;
    display: flex;

    .map {
      width: 40%;
      position: relative;
      flex-grow: 1;
      margin: 0 1.5vw;
      :deep(.amap-marker-label) {
        background-color: #3490f5;
        border: 0px;
        border-radius: 30%;
        position: relative;
      }
      #container {
        padding: 0px;
        margin: 0px;
        width: 100%;
        height: 99%;
        margin: 0.5vh 0;
      }
      .btn-box {
        display: flex;
        width: 100%;
        justify-content: center;
      }

      .icon {
        position: absolute;
        top: 10px;
        right: 5px;
        z-index: 10;
        width: 20px;
        height: 25px;
        box-shadow: 1px 1px 1px 1px rgb(0, 0, 0, 0.4);
        border-radius: 10%;
        background-color: #72e4ff;
      }
    }

    .data {
      width: 60%;
      position: relative;

      .input {
        display: flex;
        flex-direction: column;
        position: absolute;
        top: 20px;
        left: 30px;

        .searchContent {
          z-index: 100;
          width: 440px;
          height: 250px;
          background-color: #000032;
          border: 1px solid #fff;
          .closeBold {
            position: absolute;
            cursor: pointer;
            right: 10px;
            top: 40px;
            font-size: 20px;
          }

          .content {
            position: absolute;
            top: 70px;
            left: 20px;
          }

          .btns {
            display: flex;
            justify-content: center;

            // position: absolute;
            top: 150px;
            left: 100px;
          }
        }
      }
    }
    .table {
      width: 90%;
      position: absolute;
      left: 30px;
      top: 120px;
    }
    .el-pagination {
      display: flex;
      justify-content: center;
    }
  }
  .btns {
    position: absolute;
    top: 80px;
    left: 30px;

    .btn {
      width: max-content;
      height: 30px;
    }
    // :deep(.el-button) {
    //   color: #003766;
    //   border: 2px solid #7c9dcb !important;
    // }
  }
</style>
