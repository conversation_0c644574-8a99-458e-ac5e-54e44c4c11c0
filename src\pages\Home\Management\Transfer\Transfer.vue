<template>
  <div class="route">
    <div class="map">
      <div id="container"></div>
      <div class="position-message" v-if="appearPositionMessage">
        <template
          v-for="(item, index) in clusterStore.storeResult"
          :key="index"
        >
          <div>{{ index + 1 }} : {{ item.accumulationAddress }}</div>
        </template>
      </div>
    </div>
    <div class="data" style="width: 60%">
      <BorderBox9 :color="['#90ade8', '#90ade8']">
        <div class="input">
          <el-input
            style="width: 440px"
            placeholder="请点击搜索"
            @click="openSearch"
          />
          <div class="searchContent" v-if="searchOpen">
            <div class="off" @click="closeSearch">x</div>
            <div class="content">
              <div class="group">
                <el-form label-width="auto" ref="searchModal" :model="formData">
                  <el-form-item
                    label="对接点名称"
                    prop="transitDepotSearchName"
                    style="width: 360px"
                  >
                    <el-input
                      placeholder="点击输入"
                      v-model="formData.transitDepotSearchName"
                    >
                    </el-input>
                  </el-form-item>
                  <div class="flex" style="margin-left: -20px">
                    <el-form-item
                      label="所属班组"
                      prop="teamSearchName"
                      style="width: 200px"
                    >
                      <el-select
                        placeholder="请选择"
                        v-model="formData.teamSearchName"
                        @change="getDeliveryInfo"
                      >
                        <el-option
                          :label="item.teamName"
                          :value="item.teamName"
                          v-for="item in store.transferInfo"
                          :key="item.teamName + '9'"
                        />
                      </el-select>
                    </el-form-item>
                    <el-form-item
                      label="启用状态"
                      prop="statusSearch"
                      style="width: 220px"
                    >
                      <el-select
                        placeholder="请选择"
                        v-model="formData.statusSearch"
                      >
                        <el-option label="启用" value="启用" />
                        <el-option label="禁用" value="禁用" />
                      </el-select>
                    </el-form-item>
                  </div>
                  <div class="flex" style="margin-left: -20px">
                    <el-form-item
                      label="配送类型"
                      prop="deliverySearchType"
                      style="width: 200px"
                    >
                      <el-select
                        placeholder="请选择"
                        v-model="formData.deliverySearchType"
                      >
                        <el-option
                          :label="item"
                          :value="item"
                          v-for="item in deliveryInfo.deliveryType"
                          :key="item + '9'"
                        />
                      </el-select>
                    </el-form-item>
                    <el-form-item
                      label="对接配送域"
                      prop="deliverySearchName"
                      style="margin-left: 20px; width: 200px"
                    >
                      <el-select
                        placeholder="请选择"
                        v-model="formData.deliverySearchName"
                      >
                        <el-option
                          :label="item"
                          :value="item"
                          v-for="item in deliveryInfo.deliveryName"
                          :key="item + '9'"
                        />
                      </el-select>
                    </el-form-item>
                  </div>
                </el-form>

                <div class="btns">
                  <div class="flex">
                    <el-button @click="resetSearch">清空</el-button>
                    <el-button @click="confirmSearch">搜索</el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="btns">
          <el-button
            :icon="Plus"
            class="btn"
            @click="openAdd"
            v-if="hasOp('data-management:transitDepot:add')"
            >添加对接点</el-button
          >
          <el-button
            :icon="Delete"
            class="btn"
            style="margin-left: 20px"
            @click="openDelete"
            v-if="hasOp('data-management:transitDepot:delete')"
            >删除对接点</el-button
          >
          <el-button
            :icon="EditPen"
            class="btn"
            @click="openChange"
            style="margin-left: 20px"
            v-if="hasOp('data-management:transitDepot:update')"
            >修改信息</el-button
          >
        </div>
        <div class="table" v-if="hasOp('data-management:transitDepot:view')">
          <el-table
            ref="tableRef"
            :data="transitDepotList"
            :cell-style="{ textAlign: 'center' }"
            :header-cell-style="{
              height: '1.5vh',
              'text-align': 'center',
            }"
            size="small"
            :row-style="{ height: '1vh' }"
            style="font-size: 0.8vw"
            @selection-change="handleSelect"
          >
            <el-table-column type="selection" min-width="2%" />
            <el-table-column
              label="序号"
              min-width="2%"
              type="index"
              :index="Nindex"
            />
            <el-table-column
              label="对接点名称"
              min-width="4%"
              prop="transitDepotName"
            />
            <el-table-column label="经纬度" min-width="4%">
              <template #default="scope">
                <div>
                  {{
                    "经度:" +
                    scope.row.longitude +
                    "," +
                    "纬度:" +
                    scope.row.latitude
                  }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="所属班组" min-width="3%" prop="groupName">
              <template #default="scope">
                <div>
                  {{ scope.row.groupName ? scope.row.groupName : "无" }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="启用状态" min-width="3%" prop="status" />
            <el-table-column
              label="配送类型"
              min-width="3%"
              prop="deliveryType"
            >
              <template #default="scope">
                <div>
                  {{ scope.row.deliveryType ? scope.row.deliveryType : "无" }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              label="对接配送域"
              min-width="4%"
              prop="deliveryName"
            >
              <template #default="scope">
                <div>
                  {{ scope.row.deliveryName ? scope.row.deliveryName : "无" }}
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </BorderBox9>
      <el-pagination
        v-if="!inSearch"
        layout="prev, pager, next"
        :current-page="searchData.pageNum"
        :page-size="searchData.pageSize"
        :total="store.transferTotalList.length"
        @current-change="handlePageChange"
        style="display: flex; justify-content: center"
      />
      <el-pagination
        v-if="inSearch"
        layout="prev, pager, next"
        :current-page="searchData.pageNum"
        :page-size="searchData.pageSize"
        :total="transitDepotList.length"
        @current-change="handlePageChange"
        style="display: flex; justify-content: center"
      />
      <div class="areaDialog">
        <addTransfer ref="addTransferRef" @confirmAdd="confirmAdd" />
        <changeTransfer
          v-if="info"
          ref="changeTransferRef"
          :info="info"
          :transitDepotId="transitDepotId"
          :transitDepotName="transitDepotName"
          :deliveryType="deliveryType"
          @confirmChange="confirmChange"
        />
        <deleteTransfer
          ref="deleteTransferRef"
          :transitDepotId="transitDepotId"
          @confirmDelete="confirmDelete"
        />
        <pathDialog ref="pathRef" dType="对接点"></pathDialog>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { MAP_KEY, SECURITY_CODE } from "@/utils/getMapKey";
  import pathDialog from "../Path/pathDialog.vue";
  import { hasOp } from "@/op";
  import { Plus, Delete, EditPen } from "@element-plus/icons-vue";
  import { BorderBox9 } from "@dataview/datav-vue3";
  import AMapLoader from "@amap/amap-jsapi-loader";
  import { modifyUserAgent } from "@/utils/modifyUserAgent";
  import { useClusterStore } from "@/store/cluster";
  import { useDeliveryStore } from "@/store/delivery";
  import { useTransferStore } from "@/store/managerment/transfer";
  import { data1, data2, data3, data4, data5 } from "./data/data";
  import addTransfer from "./addTransfer/addTransfer.vue";
  import changeTransfer from "./changeTransfer/changeTransfer.vue";
  import deleteTransfer from "./deleteTransfer/deleteTransfer.vue";

  window._AMapSecurityConfig = {
    securityJsCode: SECURITY_CODE,
  };
  modifyUserAgent();

  //聚集区Store
  const clusterStore = useClusterStore();
  var map: any = null;
  AMapLoader.load({
    key: MAP_KEY, // 申请好的Web端开发者Key，首次调用 load 时必填
    version: "2.0", // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
    plugins: ["AMap.DistrictSearch", "AMap.MarkerCluster"], // 需要使用的的插件列表，如比例尺'AMap.Scale'等 // 需要使用的的插件列表，如比例尺'AMap.Scale'等
  }).then(async (AMap: any) => {
    // 创建一个DistrictSearch对象，用于搜索行政区划信息。
    const district = new AMap.DistrictSearch({
      subdistrict: 1,
      extensions: "all",
      level: "province",
    });
    // 使用DistrictSearch对象搜索"韶关市"的行政区划信息，并在回调函数中处理结果
    district.search("韶关市", async function (_: any, result: any) {
      const bounds = result.districtList[0].boundaries;
      const mask = [];
      for (let i = 0; i < bounds.length; i++) {
        mask.push([bounds[i]]);
      }

      map = new AMap.Map("container", {
        // 设置地图容器id
        mask: mask, // 为Map实例制定掩模的路径,各图层将值显示路径范围内图像,3D模式下有效
        zoom: 9, // 设置当前显示级别
        expandZoomRange: true, // 开启显示范围设置
        zooms: [7, 8.5], //最小显示级别为7，最大显示级别为20
        center: [113.767587, 24.718014], // 设置地图中心点位置
        viewMode: "3D", // 特别注意,设置为3D则其他地区不显示
        zoomEnable: true, // 是否可以缩放地图
        resizeEnable: true,
      });
      //分割线涂色
      let polygon: any[] = [
        "polygon1",
        "polygon2",
        "polygon3",
        "polygon4",
        "polygon5",
      ];
      // 各班组颜色
      let colorArr = ["#f6efa6", "#b3d6e9", "#f5dbdc", "#99ccbd", "#cb9ebd"];
      let polygonData: any = [data1, data2, data3, data4, data5];
      let colorMapArr = ["#d79d46", "#6880a4", "#a9686b", "#5eaa91", "#a75b8f"];
      for (let i = 0; i < polygon.length; i++) {
        polygon[i] = new AMap.Polygon({
          path: polygonData[i],
          fillOpacity: 0.7,
          fillColor: colorArr[i],
          strokeWeight: 4,
          strokeColor: colorMapArr[i],
        });
      }
      map.add(polygon);

      // 添加文字标注
      const fontStyle2 = {
        "margin-bottom": "20rem",
        "text-align": "center",
        "font-size": "20px",
        color: "black",
        "font-weight": "800",
      };
      const fontStyle3 = {
        "margin-bottom": "20rem",
        "text-align": "center",
        "font-size": "15px",
        color: "black",
      };
      const text11 = new AMap.Text({
        text: "班组四",
        position: new AMap.LngLat(113.147669, 25.329892),
        style: fontStyle2,
      });
      const text12 = new AMap.Text({
        text: "班组二",
        position: new AMap.LngLat(113.075875, 24.915856),
        style: fontStyle2,
      });
      const text13 = new AMap.Text({
        text: "班组一",
        position: new AMap.LngLat(113.569175, 25.215764),
        style: fontStyle2,
      });
      const text14 = new AMap.Text({
        text: "班组五",
        position: new AMap.LngLat(113.949986, 24.250581),
        style: fontStyle2,
      });
      const text15 = new AMap.Text({
        text: "班组三",
        position: new AMap.LngLat(114.01184, 25.117653),
        style: fontStyle2,
      });

      const text16 = new AMap.Text({
        text: "新丰县中转站",
        position: new AMap.LngLat(114.19539, 24.05113),
        style: fontStyle3,
      });

      const text17 = new AMap.Text({
        text: "坪石镇中转站",
        position: new AMap.LngLat(113.0205, 25.2),
        style: fontStyle3,
      });
      const text18 = new AMap.Text({
        text: "翁源县中转站",
        position: new AMap.LngLat(114.14502, 24.33734),
        style: fontStyle3,
      });
      const text19 = new AMap.Text({
        text: "马市烟叶中转站",
        position: new AMap.LngLat(114.154214, 25.025656),
        style: fontStyle3,
      });

      const text20 = new AMap.Text({
        text: "班组一物流配送中心",
        position: new AMap.LngLat(113.48208, 24.964151),
        style: fontStyle3,
      });

      const text21 = new AMap.Text({
        text: "班组二物流配送中心",
        position: new AMap.LngLat(112.98208, 24.754151),
        style: fontStyle3,
      });

      map.add([
        text11,
        text12,
        text13,
        text14,
        text15,
        text16,
        text17,
        text18,
        text19,
        text20,
        text21,
      ]);
    });
  });

  const pathRef = ref<any>();
  const store = useTransferStore();
  const deliveryStore = useDeliveryStore();
  const transitDepotList = ref<any>();
  const addTransferRef = ref<InstanceType<typeof addTransfer>>();
  const changeTransferRef = ref<InstanceType<typeof changeTransfer>>();
  const deleteTransferRef = ref<InstanceType<typeof deleteTransfer>>();
  const inSearch = ref<boolean>(false);
  const tableRef = ref<any>();
  const searchOpen = ref<any>(false);
  const searchResult = ref<any>();
  const searchModal = ref<any>();

  const formData = ref<any>({
    transitDepotSearchName: "",
    teamSearchName: "",
    statusSearch: "",
    deliverySearchType: "",
    deliverySearchName: "",
  });
  const searchData = reactive({
    pageNum: 1,
    pageSize: 4,
  });

  onMounted(() => {
    store.getTransitDepotTotalData(searchData.pageSize).then(() => {
      getTableInitData(searchData.pageSize);
    });
  });

  function openSearch() {
    store.getTransitDepotDataInfo().then(() => {
      deliveryStore.getDeliverySelectList().then(() => {
        deliveryInfo.value = {
          deliveryName: deliveryStore.selectList.deliveryList,
          deliveryType: ["直送", "中转", "直送(接驳)"],
        };
        searchOpen.value = true;
      });
    });
  }
  function closeSearch() {
    searchOpen.value = false;
  }

  function resetSearch() {
    formData.value = {
      transitDepotSearchName: "",
      teamSearchName: "",
      statusSearch: "",
      deliverySearchType: "",
      deliverySearchName: "",
    };
    deliveryStore.getDeliverySelectList().then(() => {
      deliveryInfo.value = {
        deliveryName: deliveryStore.selectList.deliveryList,
        deliveryType: ["直送", "中转", "直送(接驳)"],
      };
      searchOpen.value = true;
    });
    // deliveryInfo.value = {
    //   deliveryName: [],
    //   deliveryType: ['直送','中转','直送(接驳)']
    // }
  }

  function confirmSearch() {
    if (
      !formData.value.transitDepotSearchName &&
      !formData.value.teamSearchName &&
      !formData.value.statusSearch &&
      !formData.value.deliverySearchType &&
      !formData.value.deliverySearchName
    ) {
      searchResult.value = [];
      store.getTransitDepotTotalData(searchData.pageSize).then(() => {
        getTableInitData(searchData.pageSize);
      });
      inSearch.value = false;
      return;
    }
    searchResult.value = search(store.transferTotalList, formData.value);
    searchData.pageNum = 1;
    transitDepotList.value = searchResult.value.slice(
      searchData.pageNum * searchData.pageSize - searchData.pageSize,
      searchData.pageNum * searchData.pageSize
    );
    inSearch.value = true;
    // deliveryInfo.value = {
    //   deliveryName: [],
    //   deliveryType: ['直送','中转','驳接']
    // }
  }

  const deliveryInfo = ref<any>({
    deliveryName: [],
    deliveryType: ["直送", "中转", "直送(接驳)"],
  });

  function getDeliveryInfo() {
    formData.value.deliverySearchType = "";
    formData.value.deliverySearchName = "";
    deliveryInfo.value.deliveryType = [];
    deliveryInfo.value.deliveryName = [];

    const nameInfo = store.transferInfo.find(
      (item: any) => item.teamName === formData.value.teamSearchName
    );
    if (nameInfo.deliveryName) {
      nameInfo.deliveryName.split(",").forEach((ele: any) => {
        if (!(ele === "" || !ele || ele === "null")) {
          deliveryInfo.value.deliveryName.push(ele);
        }
      });
    }
    store.transferInfo
      .find((item: any) => formData.value.teamSearchName === item.teamName)
      .deliveryType.split(",")
      .forEach((ele: any) => {
        deliveryInfo.value.deliveryType.push(ele);
      });
  }

  function search(data: any, searchParams: any) {
    return data.filter((item: any) => {
      // 根据搜索条件进行匹配
      const matchesTransitDepotSearchName = searchParams.transitDepotSearchName
        ? item.transitDepotName.includes(searchParams.transitDepotSearchName)
        : true;
      const matchesTeamSearchName = searchParams.teamSearchName
        ? item.groupName === searchParams.teamSearchName
        : true;
      const matchesStatus = searchParams.statusSearch
        ? item.status === searchParams.statusSearch
        : true;
      let matchesDeliverySearchType = searchParams.deliverySearchType
        ? item.deliveryType &&
          item.deliveryType.includes(searchParams.deliverySearchType)
        : true;
      let matchesDeliverySearchName = searchParams.deliverySearchName
        ? item.deliveryName &&
          item.deliveryName.includes(searchParams.deliverySearchName)
        : true;

      return (
        matchesTransitDepotSearchName &&
        matchesTeamSearchName &&
        matchesStatus &&
        matchesDeliverySearchType &&
        matchesDeliverySearchName
      );
    });
  }

  function openAdd() {
    store.getTransitDepotDataInfo().then(() => {
      addTransferRef.value.addTransferOpen = true;
    });
  }

  function confirmAdd() {
    store.getTransitDepotTotalData(searchData.pageSize).then(() => {
      pageQuery(searchData.pageNum, searchData.pageSize);
      ElMessage({
        message: "添加成功",
        type: "success",
      });
      pathRef.value.vis = true;
    });
  }

  function openChange() {
    if (tableRef.value.getSelectionRows().length !== 1) {
      ElMessage({
        message: "请单选一个班组修改",
        type: "warning",
      });
      return;
    }
    store.getTransitDepotDataInfo().then(() => {
      changeTransferRef.value.changeTransferOpen = true;
    });
  }

  const current = ref<number>(0);
  function confirmChange() {
    current.value = searchData.pageNum;
    store.getTransitDepotTotalData(searchData.pageSize).then(() => {
      searchData.pageNum = current.value;
      pageQuery(searchData.pageNum, searchData.pageSize);
      ElMessage({
        message: "修改成功",
        type: "success",
      });
      pathRef.value.vis = true;
    });
  }

  function openDelete() {
    if (tableRef.value.getSelectionRows().length !== 1) {
      ElMessage({
        message: "选取一个",
        type: "warning",
      });
      return;
    }
    if (
      tableRef.value
        .getSelectionRows()
        .findIndex((item: any) => item.status !== "启用") === -1
    ) {
      ElMessage({
        message: "需选取未启用的",
        type: "warning",
      });
      return;
    }
    deleteTransferRef.value.deleteVis = true;
  }

  function confirmDelete() {
    store.getTransitDepotTotalData(searchData.pageSize).then(() => {
      pageQuery(searchData.pageNum, searchData.pageSize);
      ElMessage({
        message: "删除成功",
        type: "success",
      });
    });
  }

  const transitDepotName = ref<string>();
  const transitDepotId = ref();
  const deliveryType = ref<string>();
  const info = ref<any>();
  //处理选择行
  function handleSelect(rowData: any) {
    if (rowData.length >= 1) {
      transitDepotName.value = rowData[0].transitDepotName;
      transitDepotId.value = rowData[0].transitDepotId;
      deliveryType.value = rowData[0].deliveryType;
      info.value = Object.assign({}, rowData[0]);
      if (!info.value.groupName) {
        info.value.groupName = "";
      }
      if (!info.value.deliveryName) {
        info.value.deliveryName = "";
      }
    }
  }

  //处理分页
  function handlePageChange(num: number = 1) {
    searchData.pageNum = num;
    if (inSearch.value) {
      transitDepotList.value = searchResult.value.slice(
        searchData.pageNum * searchData.pageSize - searchData.pageSize,
        searchData.pageNum * searchData.pageSize
      );
      return;
    }
    pageQuery(searchData.pageNum, searchData.pageSize);
  }

  //传入分页参数
  function pageQuery(pageNum: number, pageSize: number) {
    if (inSearch.value) {
      confirmSearch();
      return;
    }
    transitDepotList.value = store.transferTotalList.slice(
      pageNum * pageSize - pageSize,
      pageNum * pageSize
    );
  }

  //获取初始表格数据
  function getTableInitData(pageSize: number) {
    transitDepotList.value = store.transferTotalList.slice(0, pageSize);
  }

  function Nindex(index: number) {
    const page = searchData.pageNum; // 当前页码
    const pagesize = searchData.pageSize; // 每页条数
    return index + 1 + (page - 1) * pagesize;
  }
</script>

<style lang="scss" scoped>
  .flex {
    display: flex;
    justify-content: flex-start;
  }

  .position-message {
    position: absolute;
    top: 10px;
    left: 10px;
    bottom: auto;
    color: white;
    opacity: 0.7;
    font-size: 10px;
    line-height: 15px;
    background-color: rgb(87, 83, 83);
  }
  .route {
    width: 100%;
    display: flex;

    .map {
      width: 40%;
      position: relative;
      flex-grow: 1;
      margin: 0 1.5vw;
      :deep(.amap-marker-label) {
        background-color: #3490f5;
        border: 0px;
        border-radius: 30%;
        position: relative;
      }
      #container {
        padding: 0px;
        margin: 0px;
        width: 100%;
        height: 99%;
        margin: 0.5vh 0;
      }
      .btn-box {
        display: flex;
        width: 100%;
        justify-content: center;
      }

      .icon {
        position: absolute;
        top: 10px;
        right: 5px;
        z-index: 10;
        width: 20px;
        height: 25px;
        box-shadow: 1px 1px 1px 1px rgb(0, 0, 0, 0.4);
        border-radius: 10%;
        background-color: #72e4ff;
      }
    }

    .data {
      width: 60%;
      position: relative;

      .input {
        display: flex;
        flex-direction: column;
        position: absolute;
        top: 20px;
        left: 30px;

        .searchContent {
          z-index: 100;
          width: 440px;
          height: 250px;
          background-color: #000032;
          border: 1px solid #fff;
          .off {
            width: 2vw;
            height: 3vh;
            position: absolute;
            right: 0;
            color: white;
            font-size: 20px;
            text-align: center;
            cursor: pointer;
            z-index: 10;
          }

          .content {
            position: absolute;
            top: 70px;
            left: 20px;
          }

          .btns {
            position: absolute;
            top: 150px;
            left: 120px;
          }
        }
      }
    }
    .table {
      margin-top: -20px;
      width: 90%;
      position: absolute;
      left: 30px;
      top: 140px;
    }
  }
  .btns {
    position: absolute;
    top: 80px;
    left: 30px;

    .btn {
      width: max-content;
      height: 30px;
    }
    // :deep(.el-button) {
    //   color: #7c9dcb;
    //   // border: 2px solid #7c9dcb !important;
    // }
  }
</style>
