<template>
  <div class="route">
    <div class="map">
      <div id="container"></div>
      <div class="position-message" v-if="appearPositionMessage">
        <template
          v-for="(item, index) in clusterStore.storeResult"
          :key="index"
        >
          <div>{{ index + 1 }} : {{ item.accumulationAddress }}</div>
        </template>
      </div>
    </div>
    <div class="data" style="width: 60%">
      <BorderBox9 :color="['#90ade8', '#90ade8']">
        <div class="input">
          <el-input
            style="width: 440px"
            placeholder="请点击搜索"
            @click="openSearch"
          />
          <div class="searchContent" v-if="searchOpen">
            <div class="closeBold" @click="closeSearch">x</div>
            <div class="content">
              <div class="group">
                <el-form ref="searchModal" :model="formData">
                  <el-form-item
                    label="对接中转站"
                    prop="transitDepotName"
                    style="width: 300px"
                  >
                    <el-select
                      placeholder="请选择"
                      v-model="formData.transitDepotName"
                    >
                      <el-option
                        :label="item"
                        :value="item"
                        v-for="item in store.selectList.transitDepotList"
                        :key="item + '9'"
                      />
                    </el-select>
                  </el-form-item>
                  <div class="flex">
                    <el-form-item label="配送域" prop="deliveryName">
                      <el-select
                        placeholder="请选择"
                        style="min-width: 100px"
                        v-model="formData.deliveryName"
                      >
                        <el-option
                          :label="item"
                          :value="item"
                          v-for="item in store.selectList.deliveryList"
                          :key="item + '9'"
                        />
                      </el-select>
                    </el-form-item>
                    <el-form-item
                      label="配送类型"
                      prop="deliveryType"
                      style="margin-left: 20px"
                    >
                      <el-select
                        placeholder="请选择"
                        style="min-width: 100px"
                        v-model="formData.deliveryType"
                      >
                        <el-option
                          :label="item"
                          :value="item"
                          v-for="item in store.selectList.deliveryTypeList"
                          :key="item + '9'"
                        />
                      </el-select>
                    </el-form-item>
                  </div>
                  <div class="flex">
                    <el-form-item label="所属班组" prop="teamName">
                      <el-select
                        placeholder="请选择"
                        style="min-width: 100px"
                        v-model="formData.teamName"
                      >
                        <el-option
                          :label="item"
                          :value="item"
                          v-for="item in store.selectList.teamList"
                          :key="item + '9'"
                        />
                      </el-select>
                    </el-form-item>
                    <el-form-item
                      label="行政区"
                      prop="areaName"
                      style="margin-left: 20px"
                    >
                      <el-select
                        placeholder="请选择"
                        style="min-width: 100px"
                        v-model="formData.areaName"
                      >
                        <el-option
                          :label="item"
                          :value="item"
                          v-for="item in store.selectList.areaList"
                          :key="item + '9'"
                        />
                      </el-select>
                    </el-form-item>
                  </div>
                </el-form>

                <div class="btns">
                  <el-button type="primary" @click="resetSearch"
                    >清空</el-button
                  >
                  <el-button type="primary" @click="confirmSearch"
                    >搜索</el-button
                  >
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="btns">
          <el-button
            :icon="Plus"
            class="btn"
            @click="openAdd"
            v-if="hasOp('data-management:delivery:add')"
            >添加配送域</el-button
          >
          <el-button
            :icon="Delete"
            class="btn"
            style="margin-left: 20px"
            @click="openDelete"
            v-if="hasOp('data-management:delivery:delete')"
            >删除配送域</el-button
          >
          <el-button
            :icon="EditPen"
            class="btn"
            @click="openChange"
            style="margin-left: 20px"
            v-if="hasOp('data-management:delivery:update')"
            >修改信息</el-button
          >
        </div>
        <div class="table" v-if="hasOp('data-management:delivery:view')">
          <el-table
            ref="tableRef"
            :data="deliveryList"
            :cell-style="{ textAlign: 'center' }"
            :header-cell-style="{
              height: '4vh',
              'text-align': 'center',
            }"
            size="small"
            :row-style="{ height: '3.9vh' }"
            style="font-size: 0.8vw"
            @selection-change="handleSelect"
          >
            <el-table-column type="selection" min-width="1%" />
            <el-table-column
              label="序号"
              min-width="2%"
              type="index"
              :index="Nindex"
            />
            <el-table-column
              label="配送域名称"
              min-width="3%"
              prop="deliverName"
            />
            <el-table-column label="所属班组" min-width="3%" prop="teamName">
              <template #default="scope">
                <div>{{ scope.row.teamName ? scope.row.teamName : "无" }}</div>
              </template>
            </el-table-column>
            <el-table-column
              label="配送类型"
              min-width="3%"
              prop="deliveryAreaType"
            />
            <el-table-column label="路径数" min-width="2%" prop="routeNumber" />
            <el-table-column label="车辆数" min-width="2%" prop="carNumber" />
            <el-table-column
              label="对接中转站"
              min-width="4%"
              prop="transitDepotName"
            >
              <template #default="scope">
                <div>
                  {{
                    scope.row.transitDepotName
                      ? scope.row.transitDepotName
                      : "无"
                  }}
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </BorderBox9>
      <div class="areaDialog">
        <addDelivery ref="addDeliveryRef" @confirmAdd="confirmAdd" />
        <changeDelivery
          v-if="info"
          ref="changeDeliveryRef"
          @confirmChange="confirmChange"
          :deliveryInfo="info"
        />
        <deleteDelivery
          v-if="info"
          ref="deleteDeliveryRef"
          @confirmDelete="confirmDelete"
          :id="info.deliveryId"
        />
        <pathDialog ref="pathRef" dType="配送域"></pathDialog>
      </div>
      <div class="pageDivide" style="display: flex; justify-content: center">
        <el-pagination
          v-if="!inSearch"
          layout="prev, pager, next"
          :current-page="searchData.pageNum"
          :page-size="searchData.pageSize"
          :total="store.deliveryTotalList.length"
          @current-change="handlePageChange"
        />
        <el-pagination
          v-if="inSearch"
          layout="prev, pager, next"
          :current-page="searchData.pageNum"
          :page-size="searchData.pageSize"
          :total="deliveryList.length"
          @current-change="handlePageChange"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { MAP_KEY, SECURITY_CODE } from "@/utils/getMapKey";
  import pathDialog from "../Path/pathDialog.vue";
  import { hasOp } from "@/op";
  import { Plus, Delete, EditPen, CloseBold } from "@element-plus/icons-vue";
  import { BorderBox9 } from "@dataview/datav-vue3";
  import AMapLoader from "@amap/amap-jsapi-loader";
  import { useClusterStore } from "@/store/cluster";
  import { useDeliveryStore } from "@/store/delivery";
  import { data1, data2, data3, data4, data5 } from "./data/data";

  import addDelivery from "./addDelivery/addDelivery.vue";
  import changeDelivery from "./changeDelivery/changeDelivery.vue";
  import deleteDelivery from "./deleteDelivery/deleteDelivery.vue";
  import { IDeliveryAddList, IDeliveryUpdateList } from "@/types/delivery";
  import { modifyUserAgent } from "@/utils/modifyUserAgent";
  modifyUserAgent();

  window._AMapSecurityConfig = {
    securityJsCode: SECURITY_CODE,
  };

  //聚集区Store
  const clusterStore = useClusterStore();
  //配送域store
  const store = useDeliveryStore();
  var map: any = null;
  AMapLoader.load({
    key: MAP_KEY, // 申请好的Web端开发者Key，首次调用 load 时必填
    version: "2.0", // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
    plugins: ["AMap.DistrictSearch", "AMap.MarkerCluster"], // 需要使用的的插件列表，如比例尺'AMap.Scale'等 // 需要使用的的插件列表，如比例尺'AMap.Scale'等
  }).then(async (AMap: any) => {
    // 创建一个DistrictSearch对象，用于搜索行政区划信息。
    const district = new AMap.DistrictSearch({
      subdistrict: 1,
      extensions: "all",
      level: "province",
    });
    // 使用DistrictSearch对象搜索"韶关市"的行政区划信息，并在回调函数中处理结果
    district.search("韶关市", async function (_: any, result: any) {
      const bounds = result.districtList[0].boundaries;
      const mask = [];
      for (let i = 0; i < bounds.length; i++) {
        mask.push([bounds[i]]);
      }

      map = new AMap.Map("container", {
        // 设置地图容器id
        mask: mask, // 为Map实例制定掩模的路径,各图层将值显示路径范围内图像,3D模式下有效
        zoom: 9, // 设置当前显示级别
        expandZoomRange: true, // 开启显示范围设置
        zooms: [7, 8.5], //最小显示级别为7，最大显示级别为20
        center: [113.767587, 24.718014], // 设置地图中心点位置
        viewMode: "3D", // 特别注意,设置为3D则其他地区不显示
        zoomEnable: true, // 是否可以缩放地图
        resizeEnable: true,
      });
      //分割线涂色
      let polygon: any[] = [
        "polygon1",
        "polygon2",
        "polygon3",
        "polygon4",
        "polygon5",
      ];
      // 各班组颜色
      let colorArr = ["#f6efa6", "#b3d6e9", "#f5dbdc", "#99ccbd", "#cb9ebd"];
      let polygonData: any = [data1, data2, data3, data4, data5];
      let colorMapArr = ["#d79d46", "#6880a4", "#a9686b", "#5eaa91", "#a75b8f"];
      for (let i = 0; i < polygon.length; i++) {
        polygon[i] = new AMap.Polygon({
          path: polygonData[i],
          fillOpacity: 0.7,
          fillColor: colorArr[i],
          strokeWeight: 4,
          strokeColor: colorMapArr[i],
        });
      }
      map.add(polygon);

      // 添加文字标注
      const fontStyle2 = {
        "margin-bottom": "20rem",
        "text-align": "center",
        "font-size": "20px",
        color: "black",
        "font-weight": "800",
      };
      const fontStyle3 = {
        "margin-bottom": "20rem",
        "text-align": "center",
        "font-size": "15px",
        color: "black",
      };
      const text11 = new AMap.Text({
        text: "班组四",
        position: new AMap.LngLat(113.147669, 25.329892),
        style: fontStyle2,
      });
      const text12 = new AMap.Text({
        text: "班组二",
        position: new AMap.LngLat(113.075875, 24.915856),
        style: fontStyle2,
      });
      const text13 = new AMap.Text({
        text: "班组一",
        position: new AMap.LngLat(113.569175, 25.215764),
        style: fontStyle2,
      });
      const text14 = new AMap.Text({
        text: "班组五",
        position: new AMap.LngLat(113.949986, 24.250581),
        style: fontStyle2,
      });
      const text15 = new AMap.Text({
        text: "班组三",
        position: new AMap.LngLat(114.01184, 25.117653),
        style: fontStyle2,
      });

      const text16 = new AMap.Text({
        text: "新丰县中转站",
        position: new AMap.LngLat(114.19539, 24.05113),
        style: fontStyle3,
      });

      const text17 = new AMap.Text({
        text: "坪石镇中转站",
        position: new AMap.LngLat(113.0205, 25.2),
        style: fontStyle3,
      });
      const text18 = new AMap.Text({
        text: "翁源县中转站",
        position: new AMap.LngLat(114.14502, 24.33734),
        style: fontStyle3,
      });
      const text19 = new AMap.Text({
        text: "马市烟叶中转站",
        position: new AMap.LngLat(114.154214, 25.025656),
        style: fontStyle3,
      });

      const text20 = new AMap.Text({
        text: "班组一物流配送中心",
        position: new AMap.LngLat(113.48208, 24.964151),
        style: fontStyle3,
      });

      const text21 = new AMap.Text({
        text: "班组二物流配送中心",
        position: new AMap.LngLat(112.98208, 24.754151),
        style: fontStyle3,
      });

      map.add([
        text11,
        text12,
        text13,
        text14,
        text15,
        text16,
        text17,
        text18,
        text19,
        text20,
        text21,
      ]);
    });
  });
  const addDeliveryRef = ref<InstanceType<typeof addDelivery>>();
  const changeDeliveryRef = ref<InstanceType<typeof changeDelivery>>();
  const deleteDeliveryRef = ref<InstanceType<typeof deleteDelivery>>();
  const deliveryList = ref<any>();
  const inSearch = ref<boolean>(false);
  const tableRef = ref<any>();
  const searchResult = ref<any>([]);
  const searchOpen = ref<boolean>(false);
  const searchModal = ref<any>();
  const formData = ref<any>({
    transitDepotName: "",
    teamName: "",
    areaName: "",
    deliveryType: "",
    deliveryName: "",
  });
  const searchData = reactive({
    pageNum: 1,
    pageSize: 7,
  });

  onMounted(() => {
    store.getTotalDelivery(searchData.pageSize).then(() => {
      getTableInitData(searchData.pageSize);
    });
  });

  function openSearch() {
    store.getDeliverySelectList().then(() => {
      searchOpen.value = true;
    });
  }
  function closeSearch() {
    searchOpen.value = false;
  }

  function resetSearch() {
    formData.value = {
      transitDepotName: "",
      teamName: "",
      areaName: "",
      deliveryType: "",
      deliveryName: "",
    };
  }

  function confirmSearch() {
    if (
      !formData.value.transitDepotName &&
      !formData.value.teamName &&
      !formData.value.areaName &&
      !formData.value.deliveryType &&
      !formData.value.deliveryName
    ) {
      searchResult.value = [];
      store.getTotalDelivery(searchData.pageSize).then(() => {
        getTableInitData(searchData.pageSize);
      });
      inSearch.value = false;
      return;
    }
    searchResult.value = search(store.deliveryTotalList, formData.value);
    searchData.pageNum = 1;
    deliveryList.value = searchResult.value.slice(
      searchData.pageNum * searchData.pageSize - searchData.pageSize,
      searchData.pageNum * searchData.pageSize
    );
    inSearch.value = true;
  }

  function search(data: any, searchParams: any) {
    return data.filter((item: any) => {
      // 根据搜索条件进行匹配
      const matchesTransitDepotSearchName = searchParams.transitDepotName
        ? item.transitDepotName === searchParams.transitDepotName
        : true;
      const matchesTeamSearchName = searchParams.teamName
        ? item.teamName === searchParams.teamName
        : true;
      const matchesArea = searchParams.areaName
        ? item.area === searchParams.areaName
        : true;
      let matchesDeliverySearchType = searchParams.deliveryType
        ? item.deliveryAreaType &&
          item.deliveryAreaType.includes(searchParams.deliveryType)
        : true;
      let matchesDeliverySearchName = searchParams.deliveryName
        ? item.deliverName && item.deliverName === searchParams.deliveryName
        : true;

      return (
        matchesTransitDepotSearchName &&
        matchesTeamSearchName &&
        matchesArea &&
        matchesDeliverySearchType &&
        matchesDeliverySearchName
      );
    });
  }

  function openAdd() {
    if (inSearch.value) {
      ElMessage({
        message: "还在搜索状态中,禁止打开弹窗",
        type: "warning",
      });
      return;
    }
    store.getDeliveryAddDownBox().then(() => {
      addDeliveryRef.value.addDeliveryOpen = true;
    });
  }

  function openChange() {
    if (tableRef.value.getSelectionRows().length !== 1) {
      ElMessage({
        message: "请单选一个实情修改",
        type: "warning",
      });
      return;
    }
    store.getDeliveryAddDownBox().then(() => {
      changeDeliveryRef.value.changeDeliveryOpen = true;
    });
  }

  //打开删除框
  function openDelete() {
    if (inSearch.value) {
      ElMessage({
        message: "还在搜索状态中,禁止打开弹窗",
        type: "warning",
      });
      return;
    }
    if (tableRef.value.getSelectionRows().length !== 1) {
      ElMessage({
        message: "请选择一个配送域删除!",
        type: "warning",
      });
      return;
    }
    if (info.value.teamName) {
      ElMessage({
        message: "请选择班组为无的!",
        type: "warning",
      });
      return;
    }
    deleteDeliveryRef.value.deleteVis = true;
  }

  function confirmDelete() {
    deleteDeliveryRef.value.deleteVis = false;
    store.getTotalDelivery(searchData.pageSize).then(() => {
      pageQuery(searchData.pageNum, searchData.pageSize);
      ElMessage({
        message: "删除成功!",
        type: "success",
      });
    });
  }

  const pathRef = ref<any>();

  function confirmAdd(data: IDeliveryAddList) {
    store.addDelivery(data).then((res) => {
      if (res.code === 50001 || res.message === "系统异常") {
        ElMessage.error("系统异常!");
        return;
      }
      if (data.teamName !== "") {
        pathRef.value.vis = true;
      }
      store.getTotalDelivery(searchData.pageSize).then(() => {
        pageQuery(searchData.pageNum, searchData.pageSize);
        ElMessage({
          message: "添加成功!",
          type: "success",
        });
      });
    });
  }

  const current = ref<number>(0);
  function confirmChange(data: IDeliveryUpdateList) {
    current.value = searchData.pageNum;
    store.updateDelivery(data).then((res) => {
      if (res.code === 50001 || res.message === "系统异常") {
        ElMessage.error("系统异常!");
        return;
      }
      store.getTotalDelivery(searchData.pageSize).then(() => {
        searchData.pageNum = current.value;
        pageQuery(searchData.pageNum, searchData.pageSize);
        ElMessage({
          message: "修改成功!",
          type: "success",
        });
        pathRef.value.vis = true;
      });
    });
  }

  //处理选择行
  const info = ref<any>();
  function handleSelect(rowData: any) {
    if (rowData.length >= 1) {
      info.value = Object.assign({}, rowData[0]);
      if (!info.value.teamName) {
        info.value.teamName = "";
      }
      if (!info.value.transitDepotNam) {
        info.value.transitDepotNam = "";
      }
    }
  }

  //处理分页
  function handlePageChange(num: number = 1) {
    searchData.pageNum = num;
    if (inSearch.value) {
      deliveryList.value = searchResult.value.slice(
        searchData.pageNum * searchData.pageSize - searchData.pageSize,
        searchData.pageNum * searchData.pageSize
      );
      return;
    }
    pageQuery(searchData.pageNum, searchData.pageSize);
  }

  //传入分页参数
  function pageQuery(pageNum: number, pageSize: number) {
    if (inSearch.value) {
      confirmSearch();
      return;
    }
    deliveryList.value = store.deliveryTotalList.slice(
      pageNum * pageSize - pageSize,
      pageNum * pageSize
    );
  }

  //获取初始表格数据
  function getTableInitData(pageSize: number) {
    deliveryList.value = store.deliveryTotalList.slice(0, pageSize);
  }

  function Nindex(index: number) {
    const page = searchData.pageNum; // 当前页码
    const pagesize = searchData.pageSize; // 每页条数
    return index + 1 + (page - 1) * pagesize;
  }
</script>

<style lang="scss" scoped>
  .flex {
    display: flex;
  }
  .position-message {
    position: absolute;
    top: 10px;
    left: 10px;
    bottom: auto;
    color: white;
    opacity: 0.7;
    font-size: 10px;
    line-height: 15px;
    background-color: rgb(87, 83, 83);
  }
  .route {
    width: 100%;
    display: flex;

    .map {
      width: 40%;
      position: relative;
      flex-grow: 1;
      margin: 0 1.5vw;
      :deep(.amap-marker-label) {
        background-color: #3490f5;
        border: 0px;
        border-radius: 30%;
        position: relative;
      }
      #container {
        padding: 0px;
        margin: 0px;
        width: 100%;
        height: 99%;
        margin: 0.5vh 0;
      }
      .btn-box {
        display: flex;
        width: 100%;
        justify-content: center;
      }

      .icon {
        position: absolute;
        top: 10px;
        right: 5px;
        z-index: 10;
        width: 20px;
        height: 25px;
        box-shadow: 1px 1px 1px 1px rgb(0, 0, 0, 0.4);
        border-radius: 10%;
        background-color: #72e4ff;
      }
    }

    .data {
      width: 60%;
      position: relative;

      .input {
        display: flex;
        flex-direction: column;
        position: absolute;
        top: 20px;
        left: 30px;

        .searchContent {
          z-index: 100;
          width: 450px;
          height: 250px;
          background-color: #000032;
          border: 1px solid #fff;
          .closeBold {
            position: absolute;
            font-size: 20px;
            right: 10px;
            cursor: pointer;
            top: 50px;
          }

          .content {
            position: absolute;
            top: 70px;
            left: 20px;
          }

          .btns {
            position: absolute;
            top: 150px;
            left: 100px;
          }
        }
      }
    }
    .table {
      width: 90%;
      position: absolute;
      left: 30px;
      top: 140px;
    }
  }
  .btns {
    position: absolute;
    top: 80px;
    left: 30px;

    .btn {
      width: max-content;
      height: 30px;
    }
    // :deep(.el-button) {
    //   color: #7c9dcb;
    //   border: 2px solid #7c9dcb !important;
    // }
  }
  :deep(.el-pagination) {
    margin-bottom: 0;
  }
</style>
