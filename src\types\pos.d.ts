export interface IAddLocation {
  latitude: number;
  longitude: number;
  pickupAddress: string;
  pickupName: string;
  status: number;
  type: string;
}

export interface IUpdateLocation {
  id: number;
  latitude: number;
  longitude: number;
  pickupAddress: string;
  pickupName: string;
  status: number;
  type: string;
}

export interface IGetLocationList {
  pageNum: number;
  pageSize: number;
  pickupAddress?: string;
  pickupName?: string;
  status?: number;
  type?: string;
}

//导入日志
export interface ILogLocationImport {
  pageNum: number;
  pageSize: number;
  type: string;
}

//下载空表格
export interface IFromDownload {
  code: number;
}

//下载日志
export interface ILogDownload {
  fileName: string;
  importTime: string;
}

//删除日志
export interface IDeleteLog {
  logsId: number;
}

//编辑
export interface IEditLocation {
  ids: number[];
}
