import requests from "../index";
import { IRequest } from "../request/type";
import { ILogDelete, ILogDownload, ILogImport } from "@/types/car";
import {
  ImanagementSearchData,
  IupdateArea,
  IOptionalData,
  ImanagementStoreDetial,
} from "@/types/datamanagement";
// 表格信息
export function getStore(params: ImanagementSearchData) {
  return requests.get<IRequest<any>>({
    timeout: 1000 * 60 * 3,
    url: "/datamanagement/list",
    params,
  });
}
// 添加商铺
export function addStore(data: ImanagementStoreDetial) {
  return requests.post<IRequest<any>>({
    url: "/datamanagement/add",
    data,
  });
}

// 添加打卡点
export function addPoint(data: any) {
  return requests.post<IRequest<any>>({
    url: "/datamanagement/addSpecialPoint",
    data,
  });
}

// 删除商铺
export function deleteStore(storeIdList: string) {
  return requests.delete<IRequest<any>>({
    url: `/datamanagement/delete/${storeIdList}`,
  });
}
// 获取下拉框数据
export function getOptional(dataType: string) {
  return requests.get<IRequest<IOptionalData[]>>({
    url: `/datamanagement/getOptionalData/${dataType}`,
  });
}
// 修改行政区
export function updateArea(data: IupdateArea) {
  return requests.post<IRequest<any>>({
    url: "/datamanagement/updateArea",
    data,
  });
}
// 更新商铺
export function updateStore(data: ImanagementStoreDetial) {
  return requests.post<IRequest<any>>({
    url: "/datamanagement/update",
    data,
  });
}
// 获取商铺详情
export function getStoreDetial(storeId: number) {
  return requests.get<IRequest<ImanagementStoreDetial>>({
    url: `/datamanagement/get/${storeId}`,
  });
}

//导入实情
export function importStore(formData: FormData, config: any) {
  return requests.post<IRequest<any>>({
    timeout: 1000 * 60 * 3,
    url: "/datamanagement/storeImport",
    data: formData,
    headers: {
      accept: "*/*",
      "Content-Type": "multipart/form-data",
    },
    onUploadProgress: config.onUploadProgress,
    signal: config.signal,
  });
}

//导入日志
export function importLog(params: ILogImport) {
  return requests.get<IRequest<ILogImport>>({
    url: "/datamanagement/getImportLogs",
    params,
    timeout: 1000 * 60 * 3,
  });
}

//删除日志
export function deleteLog(params: ILogDelete) {
  return requests.delete<IRequest<ILogDelete>>({
    url: "/datamanagement/deleteImportLogs",
    params,
  });
}

//下载日志
export function downloadLog(params: ILogDownload) {
  return requests.get<IRequest<ILogDownload>>({
    url: "/datamanagement/downloadLogs",
    headers: { "Content-Type": "application/x-download" },
    responseType: "blob",
    params,
  });
}

//获取下拉框
export function getSelect() {
  return requests.get<IRequest<any>>({
    url: "/datamanagement/getOptionalData/area",
  });
}

//获取打卡点
export function getAccumulation() {
  return requests.get<IRequest<any>>({
    url: "/datamanagement/getCheckInPoint",
  });
}

// 显示客户编码
export function getCode() {
  return requests.post<IRequest<any>>({
    url: "/datamanagement/getAddStoreDownBox",
  });
}
