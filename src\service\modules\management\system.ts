import request from "../../index";

import { IRequest } from "../../request/type";
//删除
export interface ISystemDelete {
  id: number;
}

//添加车辆
export function getSystemInfo() {
  return request.get<IRequest<any>>({
    url: "/datamanagement/getSystemParameterInfo",
  });
}

export function updateSystemInfo(params: any, data: any) {
  return request.post<IRequest<any>>({
    url: "/datamanagement/updateSystemParameter",
    timeout: 5 * 60 * 1000,
    headers: { "Content-Type": "application/json" },
    params,
    data,
  });
}

export function deleteSystemInfo(params: any) {
  return request.delete<IRequest<ISystemDelete>>({
    url: "/datamanagement/deleteSecondTransit",
    params,
  });
}
