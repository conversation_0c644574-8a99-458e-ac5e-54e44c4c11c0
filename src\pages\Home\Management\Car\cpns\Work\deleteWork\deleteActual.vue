<template>
    <el-dialog
      v-model="deleteVis"
      title="删除实情信息"
      class="transform"
      width="40%"
      align-center
      :close-on-click-modal="false"
    >
      <div class="content">确定删除所选实情</div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelDialog" type="primary">取消</el-button>
          <el-button type="primary" @click="confirmDialog">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </template>
  
  <script setup lang="ts">
  import { ICarDeleteData } from '@/types/car';
  import { carStore } from '@/store/managerment/car';
  const store = carStore()
  const deleteVis = ref<boolean>(false)
  const deleteEmit = defineEmits(["confirmDelete"]);
  const props = defineProps(["carId"])
  
  
  defineExpose({
  deleteVis
  });
  
  function cancelDialog() {
  deleteVis.value = false
  }
  
  function confirmDialog() {
    const data : ICarDeleteData = {
      carId: Number(props.carId)
    }
    store.deleteCarActual(data).then((res)=> {
      if(res.code === 50001 || res.message === '系统异常') {
      ElMessage.error('系统异常!')
      return
      }
      deleteVis.value = false
      deleteEmit("confirmDelete")
    })
  }
  
  </script>
  
  <style lang="less" scoped>
  .transform {
    .content {
      display: flex;
      justify-content: center;
      font-size: 20px;
    }
  }
  </style>
  