<!-- <template>
  <div class="frame1">
    <div class="title"></div>
    <div class="work">
      <div class="title">{{ weekNumber }} {{ weekArea }}</div>
      <div class="echarts">
        <div class="echart1">
          <div ref="chartRef1" style="width: 100%; height: 100%"></div>
        </div>
        <div class="echart2">
          <div ref="chartRef2" style="width: 100%; height: 100%"></div>
        </div>
        <div class="button">
          <el-date-picker
            v-model="rescreenTime"
            type="week"
            format="YYYY 第 ww 周"
            placeholder="选择时间"
            value-format="YYYY/MM/DD"
            :editable="false"
            prefix-icon=""
            @clear="rescreenTime = ''"
            @visible-change="changeWeek"
          />
          <el-button
            type="primary"
            @click="analysis"
            :style="{ marginLeft: '0px', marginTop: '50px' }"
            >点击分析</el-button
          >
        </div>
      </div>
    </div>
  </div>
  <div class="frame2">
    <div class="work">
      <div class="toolbox">
        <div style="display: flex">
          <div class="top">
            <el-input
              class="input"
              placeholder="请点击搜索"
              @click="openSearch"
            />
            <el-button
              class="button"
              :icon="Plus"
              type="primary"
              @click="openAdd"
              v-if="hasOp('data-management:carActual:add')"
              >添加实情</el-button
            >
            <el-button
              class="button"
              :icon="Delete"
              type="primary"
              @click="openDelete"
              v-if="hasOp('data-management:carActual:delete')"
              >删除实情</el-button
            >
            <el-button
              class="button"
              :icon="EditPen"
              type="primary"
              @click="openChange"
              v-if="hasOp('data-management:carActual:update')"
              >修改信息</el-button
            >
            <el-button
              class="button"
              :icon="UploadFilled"
              type="primary"
              @click="openUpload"
              v-if="hasOp('data-management:carActual:exportForm')"
              >导入表格</el-button
            >
            <el-button
              class="button"
              :icon="Notebook"
              type="primary"
              @click="openNote"
              v-if="hasOp('data-management:carActual:seeLogs')"
              >导入日志</el-button
            >
            <div class="search" v-show="true">
              <div class="search" v-if="searchOpen">
                <div class="off" @click="closeSearch">x</div>
                <el-form class="form" :model="searchFormData" ref="searchModal">
                  <el-form-item
                    label="车牌号"
                    style="width: 45%"
                    prop="licensePlateNumber"
                  >
                    <el-select
                      style="width: 250px"
                      placeholder="请选择"
                      v-model="searchFormData.licensePlateNumber"
                    >
                      <el-option
                        :label="item"
                        :value="item"
                        v-for="item in store.carSearchInfo
                          .licensePlateNumberList"
                        :key="item + '9'"
                      />
                    </el-select>
                  </el-form-item>
                  <el-form-item
                    label="班组"
                    style="width: 47%; margin-left: 20px"
                    prop="teamName"
                  >
                    <el-select
                      style="width: 250px"
                      placeholder="请选择"
                      v-model="searchFormData.teamName"
                    >
                      <el-option
                        :label="item"
                        :value="item"
                        v-for="item in store.carSearchInfo.teamList"
                        :key="item + '9'"
                      />
                    </el-select>
                  </el-form-item>
                  <el-form-item
                    label="驾驶人"
                    style="width: 45%"
                    prop="carDriver"
                  >
                    <el-select
                      style="width: 250px"
                      placeholder="请选择"
                      v-model="searchFormData.carDriver"
                    >
                      <el-option
                        :label="item"
                        :value="item"
                        v-for="item in Object.keys(
                          store.carSearchInfo.carDriverList
                        )"
                        :key="item + '9'"
                      />
                    </el-select>
                  </el-form-item>
                  <el-form-item
                    label="时间"
                    style="width: 47%; margin-left: 20px"
                    prop="time"
                  >
                    <el-date-picker
                      v-model="searchFormData.time"
                      type="date"
                      placeholder="选择日期"
                      style="width: 150px"
                      format="YYYY/MM/DD"
                      value-format="YYYY/MM/DD"
                      @clear="searchFormData.time = ''"
                    />
                  </el-form-item>
                  <div class="btn-content">
                    <el-button type="primary" @click="resetSearch"
                      >清空</el-button
                    >
                    <el-button type="primary" @click="confirmSearch"
                      >搜索</el-button
                    >
                  </div>
                </el-form>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="table">
        <el-table
          ref="tableRef"
          :data="actualList"
          :cell-style="{ textAlign: 'center' }"
          :header-cell-style="{
            height: '0.2vh',
            'text-align': 'center',
          }"
          size="small"
          :row-style="{ height: '1.2vh' }"
          style="font-size: 0.8vw"
          @selection-change="handleSelect"
        >
          <el-table-column type="selection" min-width="1%" />
          <el-table-column
            label="序号"
            min-width="2%"
            type="index"
            :index="Nindex"
          />
          <el-table-column
            label="车牌号"
            min-width="1%"
            prop="licensePlateNumber"
          />
          <el-table-column
            label="驾驶人名称"
            min-width="1%"
            prop="carDriverName"
          />
          <el-table-column
            label="实际载货量(吨)"
            min-width="1%"
            prop="actualLoad"
          >
            <template #default="scope">
              <div>
                {{ scope.row.actualLoad ? scope.row.actualLoad : "无" }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            label="实际工作时长(小时)"
            min-width="1%"
            prop="actualTime"
          >
            <template #default="scope">
              <div>
                {{ scope.row.actualTime ? scope.row.actualTime : "无" }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="路线" min-width="2%" prop="routeName">
            <template #default="scope">
              <div>{{ scope.row.routeName ? scope.row.routeName : "无" }}</div>
            </template>
          </el-table-column>
          <el-table-column label="星期" min-width="2%" prop="week" />
          <el-table-column label="日期" min-width="2%" prop="date" />
          <el-table-column label="所属班组" min-width="2%" prop="teamName">
            <template #default="scope">
              <div>{{ scope.row.teamName ? scope.row.teamName : "无" }}</div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
  <div class="pageDivide">
    <el-pagination
      v-if="!inSearch"
      layout="prev, pager, next"
      :current-page="searchData.pageNum"
      :page-size="searchData.pageSize"
      :total="store.actualCarTotalList.length"
      @current-change="handlePageChange"
    />
    <el-pagination
      v-if="inSearch"
      layout="prev, pager, next"
      :current-page="searchData.pageNum"
      :page-size="searchData.pageSize"
      :total="actualList.length"
      @current-change="handlePageChange"
    />
  </div>

  <div class="dialog">
    <addActual ref="addActualRef" @confirmAdd="confirmAdd" />
    <deleteActual
      v-if="info"
      ref="deleteActualRef"
      @confirmDelete="confirmDelete"
      :carId="info.carId"
    ></deleteActual>
    <changeActual
      v-if="info"
      ref="changeActualRef"
      @confirmChange="confirmChange"
      :info="info"
    />
    <uploadActual ref="uploadActualRef" />
    <noteTable ref="noteRef" />
  </div>
</template>

<script setup lang="ts">
  import { dayjs } from "element-plus";
  import { hasOp } from "@/op";
  import addActual from "./addWork/addActual.vue";
  import noteTable from "./noteWork/noteTable.vue";
  import changeActual from "./changeWork/changeActual.vue";
  import deleteActual from "./deleteWork/deleteActual.vue";
  import uploadActual from "./uploadTable/uploadActual.vue";
  import { ref, onMounted } from "vue";
  import {
    Plus,
    Delete,
    EditPen,
    UploadFilled,
    Notebook,
  } from "@element-plus/icons-vue";
  import * as echarts from "echarts";
  import { carStore } from "@/store/managerment/car";
  const store = carStore();

  const chartRef1 = ref(null);
  const chartRef2 = ref(null);
  let myChart1: echarts.ECharts | null = null;
  let myChart2: echarts.ECharts | null = null;
  const transFromLegend = ref<boolean>(false);
  const addActualRef = ref<any>();
  const deleteActualRef = ref<any>();
  const changeActualRef = ref<any>();
  const uploadActualRef = ref<any>();
  const rescreenTime = ref<string>("");
  const searchResult = ref<any>([]);
  const inSearch = ref<boolean>(false);
  const searchOpen = ref<boolean>(false);
  const actualList = ref<any>();
  const searchModal = ref<any>();
  const tableRef = ref<any>();
  const noteRef = ref<any>();
  const analysisData = ref<any>([]);
  const searchFormData = ref<any>({
    time: "",
    teamName: "",
    licensePlateNumber: "",
    carDriver: "",
  });
  const changeButton = ref<boolean>(false);

  const searchData = reactive({
    pageNum: 1,
    pageSize: 5,
  });

  const isInTable = computed(() => store.isInTable);
  watch(isInTable, (newV, oldV) => {
    if (newV) {
      pageQuery(searchData.pageNum, searchData.pageSize);
      store.updateIsinTable();
    }
  });

  onMounted(() => {
    initChart();
    store.getTotalCarActual(searchData.pageSize).then(() => {
      getTableInitData(searchData.pageSize);
      getData();
    });
  });

  function changeWeek() {
    if (tableRef.value.getSelectionRows().length == 1) {
      if (select.value) {
        changeButton.value = true;
        rescreenTime.value = tableRef.value.getSelectionRows()[0].date;
      }
    }
  }

  function getWeekNumber(date: Date) {
    // 确保日期是一个有效的 Date 对象
    if (!(date instanceof Date)) {
      throw new Error("Invalid date");
    }

    // 获取该日期的年份
    const year = date.getFullYear();

    // 获取该日期是这一年的第几天
    const startOfYear = new Date(year, 0, 1);
    const days = Math.floor(
      (date.getTime() - startOfYear.getTime()) / (1000 * 60 * 60 * 24)
    );

    // 计算第几周
    return Math.ceil((days + startOfYear.getDay() + 1) / 7);
  }

  const weekNumber = computed(() => {
    if (!rescreenTime.value || tableRef.value.getSelectionRows().length == 0) {
      return " ";
    }
    if (changeButton.value) {
      changeButton.value = false; // 立即重置，确保只执行一次
      return tableRef.value.getSelectionRows()[0].date;
    }
    const date = new Date(rescreenTime.value); // 注意：月份从0开始
    return "第" + getWeekNumber(date) + "周";
  });

  const weekArea = computed(() => {
    if (!rescreenTime.value) {
      return " ";
    }
    if (tableRef.value.getSelectionRows().length == 1) {
      return "";
    }

    let currentDate: any = new Date(rescreenTime.value);
    currentDate.setDate(currentDate.getDate() + 6);
    currentDate = dayjs(currentDate).format("YYYY/MM/DD");

    return `(${rescreenTime.value} - ${currentDate})`;
  });

  //获取数据的同时将数据展示
  function getData() {
    updataChart();
  }

  //初始化图表
  function initChart() {
    const chartDom1 = chartRef1.value;
    const chartDom2 = chartRef2.value;
    myChart1 = echarts.init(chartDom1);
    myChart2 = echarts.init(chartDom2);
    const option1 = {
      title: {
        show: true,
        text: "实际载货量/吨",
        textStyle: {
          color: "#a4c5e5",
        },
        top: 0,
        left: "30%",
      },
      tooltip: {
        show: true,
      },
      legend: {
        top: 20,
        textStyle: {
          color: "white",
        },
      },
      xAxis: {
        type: "category",
        data: ["星期一", "星期二", "星期三", "星期四", "星期五"],
      },
      yAxis: {
        type: "value",
      },
    };

    const option2 = {
      title: {
        show: true,
        text: "实际工作时长/小时",
        textStyle: {
          color: "#a4c5e5",
        },
        top: 0,
        left: "30%",
      },
      tooltip: {
        show: true,
      },
      legend: {
        top: 20,
        textStyle: {
          color: "white",
        },
      },
      xAxis: {
        type: "category",
        data: ["星期一", "星期二", "星期三", "星期四", "星期五"],
      },
      yAxis: {
        type: "value",
      },
    };
    myChart1.setOption(option1);
    myChart2.setOption(option2);
    //监听
    myChart1.on("legendselectchanged", function (params) {
      if (!transFromLegend.value) {
        transFromLegend.value = true; // 设置标志为 true
        myChart2.dispatchAction({
          type: "legendToggleSelect",
          name: params.name,
        });
        transFromLegend.value = false; // 事件处理完毕，重置标志
      }
    });

    myChart2.on("legendselectchanged", function (params) {
      if (!transFromLegend.value) {
        transFromLegend.value = true; // 设置标志为 true
        myChart1.dispatchAction({
          type: "legendToggleSelect",
          name: params.name,
        });
        transFromLegend.value = false; // 事件处理完毕，重置标志
      }
    });
  }

  function getWeekNum(week: string) {
    switch (week) {
      case "星期一":
        return 1;
      case "星期二":
        return 2;
      case "星期三":
        return 3;
      case "星期四":
        return 4;
      case "星期五":
        return 5;
      case "星期六":
        return 6;
      case "星期天":
        return 7;
    }
  }

  function isDateInWeek(dateString: string, weekStartString: string) {
    const date = new Date(dateString);
    const weekStart = new Date(weekStartString);

    // 获取周开始日期的时间戳
    const weekStartTimestamp = weekStart.getTime();

    // 计算周结束日期（周开始 + 6天）
    const weekEndTimestamp = weekStartTimestamp + 6 * 24 * 60 * 60 * 1000;

    // 判断日期是否在这周内
    return (
      date.getTime() >= weekStartTimestamp && date.getTime() <= weekEndTimestamp
    );
  }

  //更新图表
  function updataChart() {
    // if (analysisData.value.length < 1) {
    //   return;
    // }
    let chartOneSeriesArr = analysisData.value.map((item: any) => {
      const dataArr = [null, null, null, null, null];
      dataArr[getWeekNum(item.week) - 1] = item.actualLoad;
      return {
        name: item.licensePlateNumber,
        data: dataArr,
        type: "bar",
      };
    });
    let chartTwoSeriesArr = analysisData.value.map((item: any) => {
      const dataArr = [null, null, null, null, null];
      dataArr[getWeekNum(item.week) - 1] = item.actualTime;
      return {
        name: item.licensePlateNumber,
        data: dataArr,
        type: "bar",
      };
    });
    myChart1.setOption(
      {
        series: chartOneSeriesArr,
      },
      { replaceMerge: "series" }
    );
    myChart2.setOption(
      {
        series: chartTwoSeriesArr,
      },
      { replaceMerge: "series" }
    );
  }

  //确认分析
  function analysis() {
    if (rescreenTime.value === "") {
      ElMessage.error("未选日期");
      return;
    }
    console.log(rescreenTime.value);
    if (tableRef.value.getSelectionRows().length === 0) {
      ElMessage.error("没有勾选");
      return;
    }
    if (tableRef.value.getSelectionRows().length > 5) {
      ElMessage.error("不要勾选超过5辆");
      return;
    }
    //去重
    const carNameList = Array.from(
      new Set([
        ...tableRef.value
          .getSelectionRows()
          .slice(0, 5)
          .map((item: any) => {
            return item.licensePlateNumber;
          }),
      ])
    );
    console.log(carNameList);
    const test = store.actualCarTotalList.filter((item: any) => {
      return carNameList.includes(item.licensePlateNumber);
    });
    analysisData.value = test.filter((item: any) => {
      return isDateInWeek(item.date, rescreenTime.value);
    });
    console.log(analysisData.value);
    updataChart();
  }

  //打开搜索框
  function openSearch() {
    store.searchCarInfoData().then(() => {
      searchOpen.value = true;
    });
  }

  //关闭搜索框
  function closeSearch() {
    searchOpen.value = false;
  }

  //重新搜索
  function resetSearch() {
    searchFormData.value = {
      time: "",
      teamName: "",
      licensePlateNumber: "",
      carDriver: "",
    };
  }

  function confirmSearch() {
    if (
      !searchFormData.value.carDriver &&
      !searchFormData.value.licensePlateNumber &&
      !searchFormData.value.teamName &&
      !searchFormData.value.time
    ) {
      inSearch.value = false;
      searchResult.value = [];
      store.getAllCarData(searchData.pageSize).then(() => {
        getTableInitData(searchData.pageSize);
      });
      return;
    }
    searchResult.value = search(store.actualCarTotalList, searchFormData.value);
    searchData.pageNum = 1;
    actualList.value = searchResult.value.slice(
      searchData.pageNum * searchData.pageSize - searchData.pageSize,
      searchData.pageNum * searchData.pageSize
    );
    inSearch.value = true;
  }

  function search(data: any, searchParams: any) {
    return data.filter((item: any) => {
      // 根据搜索条件进行匹配
      const licensePlateNumber = searchParams.licensePlateNumber
        ? item.licensePlateNumber === searchParams.licensePlateNumber
        : true;
      const matchesTeam = searchParams.teamName
        ? item.teamName === searchParams.teamName
        : true;
      const matchesDriver = searchParams.carDriver
        ? item.carDriverName === searchParams.carDriver
        : true;
      const matchesTime = searchParams.time
        ? item.date && item.date === searchParams.time
        : true;

      return licensePlateNumber && matchesTeam && matchesDriver && matchesTime;
    });
  }

  //打开添加框
  function openAdd() {
    if (inSearch.value) {
      ElMessage({
        message: "还在搜索状态中,禁止打开弹窗",
        type: "warning",
      });
      return;
    }
    store.getActualDownBox().then(() => {
      addActualRef.value.addActualOpen = true;
    });
  }

  function confirmAdd(data: any) {
    if (
      store.actualCarTotalList.findIndex((item: any) => {
        return (
          (item.licensePlateNumber === data.licensePlateNumber &&
            item.date === data.date) ||
          (item.licensePlateNumber === data.licensePlateNumber &&
            item.routeName === data.route)
        );
      }) !== -1
    ) {
      ElMessage({
        message: "含重复项!",
        type: "warning",
      });
      return;
    }
    store.addCarActual(data).then((res) => {
      if (res.code === 50001 || res.message === "系统异常") {
        ElMessage.error("系统异常!");
        return;
      }
      store.getTotalCarActual(searchData.pageSize).then(() => {
        pageQuery(searchData.pageNum, searchData.pageSize);
        ElMessage({
          message: "添加成功!",
          type: "success",
        });
      });
    });
  }

  //打开删除框
  function openDelete() {
    if (inSearch.value) {
      ElMessage({
        message: "还在搜索状态中,禁止打开弹窗",
        type: "warning",
      });
      return;
    }
    if (tableRef.value.getSelectionRows().length !== 1) {
      ElMessage({
        message: "请选择一个实情删除!",
        type: "warning",
      });
      return;
    }
    deleteActualRef.value.deleteVis = true;
  }

  function confirmDelete() {
    deleteActualRef.value.deleteVis = false;
    store.getTotalCarActual(searchData.pageSize).then(() => {
      pageQuery(searchData.pageNum, searchData.pageSize);
      ElMessage({
        message: "删除成功!",
        type: "success",
      });
    });
  }

  //打开修改框
  function openChange() {
    if (inSearch.value) {
      ElMessage({
        message: "还在搜索状态中,禁止打开弹窗",
        type: "warning",
      });
      return;
    }
    if (tableRef.value.getSelectionRows().length !== 1) {
      ElMessage({
        message: "请单选一个实情修改",
        type: "warning",
      });
      return;
    }
    changeActualRef.value.changeActualOpen = true;
  }

  const current = ref<number>(0);
  function confirmChange() {
    current.value = searchData.pageNum;
    changeActualRef.value.changeActualOpen = false;
    store.getTotalCarActual(searchData.pageSize).then(() => {
      searchData.pageNum = current.value;
      pageQuery(searchData.pageNum, searchData.pageSize);
      ElMessage({
        message: "修改成功!",
        type: "success",
      });
    });
  }

  //打开导入表格
  function openUpload() {
    uploadActualRef.value.uploadVisible = true;
  }

  //打开导入日记
  function openNote() {
    store.noteData = { total: "0" };
    nextTick(() => {
      noteRef.value.noteVisible = true;
      noteRef.value.onOpenDialog();
    });
  }

  const info = ref<any>();
  const select = ref<boolean>(false);
  //处理选择行
  function handleSelect(rowData: any) {
    if (rowData.length == 1) {
      select.value = true;
    } else {
      select.value = false;
    }
    console.log("select", select.value);
    if (rowData.length >= 1) {
      info.value = Object.assign({}, rowData[0]);
    }
    // changeWeek();
  }

  //处理分页
  function handlePageChange(num: number = 1) {
    searchData.pageNum = num;
    if (inSearch.value) {
      actualList.value = searchResult.value.slice(
        searchData.pageNum * searchData.pageSize - searchData.pageSize,
        searchData.pageNum * searchData.pageSize
      );
      return;
    }
    pageQuery(searchData.pageNum, searchData.pageSize);
  }

  //传入分页参数
  function pageQuery(pageNum: number, pageSize: number) {
    if (inSearch.value) {
      confirmSearch();
      return;
    }
    actualList.value = store.actualCarTotalList.slice(
      pageNum * pageSize - pageSize,
      pageNum * pageSize
    );
  }

  //获取初始表格数据
  function getTableInitData(pageSize: number) {
    actualList.value = store.actualCarTotalList.slice(0, pageSize);
  }

  function Nindex(index: number) {
    const page = searchData.pageNum; // 当前页码
    const pagesize = searchData.pageSize; // 每页条数
    return index + 1 + (page - 1) * pagesize;
  }
</script>

<style lang="less" scoped>
  .frame1 {
    // :deep(.el-button) {
    //   color: #7c9dcb;
    //   border: 2px solid #7c9dcb !important;
    // }
    display: flex;
    justify-content: center;
    width: 80vw;
    height: 32vh;
    .work {
      width: 90%;
      height: 90%;
      background-color: #011631;
      border: 1px solid #90ade8;
      margin-top: 30px;
      display: flex;
      flex-direction: column;
      align-items: center;
      .title {
        margin-left: -20%;
        font-size: 20px;
        margin-top: 10px;
      }
      .echarts {
        display: flex;
        width: 100%;
        height: 100%;

        .echart1 {
          width: 40%;
          height: 100%;
        }
        .echart2 {
          width: 40%;
          height: 100%;
          margin-left: 10px;
        }
        .button {
          display: flex;
          flex-direction: column;
          margin-left: 60px;
          width: 9%;
          height: 100%;
          justify-content: center;
          position: relative;
          :deep(.el-date-editor) {
            width: 6.8vw;
            height: 4.5vh !important;
            // border: 2px solid #7c9dcb !important;
            border: 0;
            background-color: #052658;
            color: #7c9dcb !important;
            font-size: 14px;
          }
          :deep(.el-input__inner::placeholder) {
            border: 0;
            color: #7c9dcb;
            font-size: 14px;
            text-align: center;
          }
          :deep(.el-input__prefix) {
            display: none;
          }
        }
      }
    }
  }
  .frame2 {
    // :deep(.el-button) {
    //   color: #7c9dcb;
    //   border: 2px solid #7c9dcb !important;
    // }
    margin-top: 0.2vh;
    width: 80vw;
    height: 47vh;
    display: flex;
    justify-content: center;
    .work {
      width: 95%;
      height: 100%;
      display: flex;
      flex-direction: column;
      .toolbox {
        .input {
          width: 400px;
          margin-left: 20px;
          margin-top: 10px;
        }
        .button {
          margin-left: 10px;
          width: max-content;
          height: 30px;
          margin-top: 10px;
        }

        .top {
          margin: 1vh 0px;

          .select {
            margin: 0 1vw;
          }

          .btn {
            box-sizing: border-box;
            width: max-content;
            // width: 2.3vw;
            height: 4vh;
          }

          .search {
            position: absolute;
            z-index: 9;
            background-color: #091b3a;
            width: 500px;

            .off {
              width: 2vw;
              height: 3vh;
              position: absolute;
              right: 0;
              color: white;
              text-align: center;
              cursor: pointer;
            }

            .form {
              display: flex;
              flex-wrap: wrap;
              padding: 20px;
              align-items: center;
              border: 1px solid #9addf6;
            }

            .btn-content {
              margin: 0 auto;
            }

            .el-form-item {
              width: 33.3%;
            }
          }
        }

        .second {
          margin: 0.5vh;
          .btn {
            margin: 0 10px;
            box-sizing: border-box;
            width: 6.5vw;
            height: 4vh;
          }
        }
      }
    }
  }

  .pageDivide {
    margin-top: 5vh;
    display: flex;
    justify-content: center;
  }

  :deep(.el-pagination) {
    margin-bottom: 0;
  }
</style> -->
<template>
  <div class="frame1">
    <div class="title"></div>
    <div class="work">
      <div class="title">{{ weekNumber }} {{ weekArea }}</div>
      <div class="echarts">
        <div class="echart1">
          <div ref="chartRef1" style="width: 100%; height: 100%"></div>
        </div>
        <div class="echart2">
          <div ref="chartRef2" style="width: 100%; height: 100%"></div>
        </div>
        <div class="button">
          <el-date-picker
            v-model="rescreenTime"
            type="week"
            format="YYYY 第 ww 周"
            placeholder="选择时间"
            value-format="YYYY/MM/DD"
            :editable="false"
            prefix-icon=""
            @clear="rescreenTime = ''"
            @visible-change="changeWeek"
          />
          <el-button
            type="primary"
            @click="analysis"
            :style="{ marginLeft: '0px', marginTop: '50px' }"
            >点击分析</el-button
          >
        </div>
      </div>
    </div>
  </div>
  <div class="frame2">
    <div class="work">
      <div class="toolbox">
        <div style="display: flex">
          <div class="top">
            <el-input
              class="input"
              placeholder="请点击搜索"
              @click="openSearch"
            />
            <el-button
              class="button"
              :icon="Plus"
              type="primary"
              @click="openAdd"
              v-if="hasOp('data-management:carActual:add')"
              >添加实情</el-button
            >
            <el-button
              class="button"
              :icon="Delete"
              type="primary"
              @click="openDelete"
              v-if="hasOp('data-management:carActual:delete')"
              >删除实情</el-button
            >
            <el-button
              class="button"
              :icon="EditPen"
              type="primary"
              @click="openChange"
              v-if="hasOp('data-management:carActual:update')"
              >修改信息</el-button
            >
            <el-button
              class="button"
              :icon="UploadFilled"
              type="primary"
              @click="openUpload"
              v-if="hasOp('data-management:carActual:exportForm')"
              >导入表格</el-button
            >
            <el-button
              class="button"
              :icon="Notebook"
              type="primary"
              @click="openNote"
              v-if="hasOp('data-management:carActual:seeLogs')"
              >导入日志</el-button
            >
            <div class="search" v-show="true">
              <div class="search" v-if="searchOpen">
                <div class="off" @click="closeSearch">x</div>
                <el-form class="form" :model="searchFormData" ref="searchModal">
                  <el-form-item
                    label="车牌号"
                    style="width: 45%"
                    prop="licensePlateNumber"
                  >
                    <el-select
                      style="width: 250px"
                      placeholder="请选择"
                      v-model="searchFormData.licensePlateNumber"
                    >
                      <el-option
                        :label="item"
                        :value="item"
                        v-for="item in store.carSearchInfo
                          .licensePlateNumberList"
                        :key="item + '9'"
                      />
                    </el-select>
                  </el-form-item>
                  <el-form-item
                    label="班组"
                    style="width: 47%; margin-left: 20px"
                    prop="teamName"
                  >
                    <el-select
                      style="width: 250px"
                      placeholder="请选择"
                      v-model="searchFormData.teamName"
                    >
                      <el-option
                        :label="item"
                        :value="item"
                        v-for="item in store.carSearchInfo.teamList"
                        :key="item + '9'"
                      />
                    </el-select>
                  </el-form-item>
                  <el-form-item
                    label="驾驶人"
                    style="width: 45%"
                    prop="carDriver"
                  >
                    <el-select
                      style="width: 250px"
                      placeholder="请选择"
                      v-model="searchFormData.carDriver"
                    >
                      <el-option
                        :label="item"
                        :value="item"
                        v-for="item in Object.keys(
                          store.carSearchInfo.carDriverList
                        )"
                        :key="item + '9'"
                      />
                    </el-select>
                  </el-form-item>
                  <el-form-item
                    label="时间"
                    style="width: 47%; margin-left: 20px"
                    prop="time"
                  >
                    <el-date-picker
                      v-model="searchFormData.time"
                      type="date"
                      placeholder="选择日期"
                      style="width: 150px"
                      format="YYYY/MM/DD"
                      value-format="YYYY/MM/DD"
                      @clear="searchFormData.time = ''"
                    />
                  </el-form-item>
                  <div class="btn-content">
                    <el-button type="primary" @click="resetSearch"
                      >清空</el-button
                    >
                    <el-button type="primary" @click="confirmSearch"
                      >搜索</el-button
                    >
                  </div>
                </el-form>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="table">
        <el-table
          ref="tableRef"
          :data="actualList"
          :cell-style="{ textAlign: 'center' }"
          :header-cell-style="{
            height: '0.2vh',
            'text-align': 'center',
          }"
          size="small"
          :row-style="{ height: '1.2vh' }"
          style="font-size: 0.8vw"
          @selection-change="handleSelect"
        >
          <el-table-column type="selection" min-width="1%" />
          <el-table-column
            label="序号"
            min-width="2%"
            type="index"
            :index="Nindex"
          />
          <el-table-column
            label="车牌号"
            min-width="1%"
            prop="licensePlateNumber"
          />
          <el-table-column
            label="驾驶人名称"
            min-width="1%"
            prop="carDriverName"
          />
          <el-table-column
            label="实际载货量(吨)"
            min-width="1%"
            prop="actualLoad"
          >
            <template #default="scope">
              <div>
                {{ scope.row.actualLoad ? scope.row.actualLoad : "无" }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            label="实际工作时长(小时)"
            min-width="1%"
            prop="actualTime"
          >
            <template #default="scope">
              <div>
                {{ scope.row.actualTime ? scope.row.actualTime : "无" }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="路线" min-width="2%" prop="routeName">
            <template #default="scope">
              <div>{{ scope.row.routeName ? scope.row.routeName : "无" }}</div>
            </template>
          </el-table-column>
          <el-table-column label="星期" min-width="2%" prop="week" />
          <el-table-column label="日期" min-width="2%" prop="date" />
          <el-table-column label="所属班组" min-width="2%" prop="teamName">
            <template #default="scope">
              <div>{{ scope.row.teamName ? scope.row.teamName : "无" }}</div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
  <div class="pageDivide">
    <el-pagination
      v-if="!inSearch"
      layout="prev, pager, next"
      :current-page="searchData.pageNum"
      :page-size="searchData.pageSize"
      :total="store.actualCarTotalList.length"
      @current-change="handlePageChange"
    />
    <el-pagination
      v-if="inSearch"
      layout="prev, pager, next"
      :current-page="searchData.pageNum"
      :page-size="searchData.pageSize"
      :total="actualList.length"
      @current-change="handlePageChange"
    />
  </div>

  <div class="dialog">
    <addActual ref="addActualRef" @confirmAdd="confirmAdd" />
    <deleteActual
      v-if="info"
      ref="deleteActualRef"
      @confirmDelete="confirmDelete"
      :carId="info.carId"
    ></deleteActual>
    <changeActual
      v-if="info"
      ref="changeActualRef"
      @confirmChange="confirmChange"
      :info="info"
    />
    <uploadActual ref="uploadActualRef" />
    <noteTable ref="noteRef" />
  </div>
</template>

<script setup lang="ts">
  import { dayjs } from "element-plus";
  import { hasOp } from "@/op";
  import addActual from "./addWork/addActual.vue";
  import noteTable from "./noteWork/noteTable.vue";
  import changeActual from "./changeWork/changeActual.vue";
  import deleteActual from "./deleteWork/deleteActual.vue";
  import uploadActual from "./uploadTable/uploadActual.vue";
  import { ref, onMounted } from "vue";
  import {
    Plus,
    Delete,
    EditPen,
    UploadFilled,
    Notebook,
  } from "@element-plus/icons-vue";
  import * as echarts from "echarts";
  import { carStore } from "@/store/managerment/car";
  const store = carStore();

  const chartRef1 = ref(null);
  const chartRef2 = ref(null);
  let myChart1: echarts.ECharts | null = null;
  let myChart2: echarts.ECharts | null = null;
  const transFromLegend = ref<boolean>(false);
  const addActualRef = ref<any>();
  const deleteActualRef = ref<any>();
  const changeActualRef = ref<any>();
  const uploadActualRef = ref<any>();
  const rescreenTime = ref<string>("");
  const searchResult = ref<any>([]);
  const inSearch = ref<boolean>(false);
  const searchOpen = ref<boolean>(false);
  const actualList = ref<any>();
  const searchModal = ref<any>();
  const tableRef = ref<any>();
  const noteRef = ref<any>();
  const analysisData = ref<any>([]);
  const searchFormData = ref<any>({
    time: "",
    teamName: "",
    licensePlateNumber: "",
    carDriver: "",
  });
  const shouldShowSelectedDate = ref<boolean>(false); // 新增：是否应该显示选中行的日期

  const searchData = reactive({
    pageNum: 1,
    pageSize: 5,
  });

  const isInTable = computed(() => store.isInTable);
  watch(isInTable, (newV, oldV) => {
    if (newV) {
      pageQuery(searchData.pageNum, searchData.pageSize);
      store.updateIsinTable();
    }
  });

  onMounted(() => {
    initChart();
    store.getTotalCarActual(searchData.pageSize).then(() => {
      getTableInitData(searchData.pageSize);
      getData();
    });
  });

  function changeWeek() {
    if (tableRef.value.getSelectionRows().length == 1) {
      shouldShowSelectedDate.value = true; // 设置显示选中行日期
      rescreenTime.value = tableRef.value.getSelectionRows()[0].date;
    }
  }

  function getWeekNumber(date: Date) {
    // 确保日期是一个有效的 Date 对象
    if (!(date instanceof Date)) {
      throw new Error("Invalid date");
    }

    // 获取该日期的年份
    const year = date.getFullYear();

    // 获取该日期是这一年的第几天
    const startOfYear = new Date(year, 0, 1);
    const days = Math.floor(
      (date.getTime() - startOfYear.getTime()) / (1000 * 60 * 60 * 24)
    );

    // 计算第几周
    return Math.ceil((days + startOfYear.getDay() + 1) / 7);
  }

  const weekNumber = computed(() => {
    if (!rescreenTime.value || tableRef.value.getSelectionRows().length == 0) {
      return " ";
    }

    // 如果应该显示选中行的日期且只选中一行，则显示选中行的日期
    if (
      shouldShowSelectedDate.value &&
      tableRef.value.getSelectionRows().length === 1
    ) {
      return tableRef.value.getSelectionRows()[0].date;
    }

    // 其他情况都返回周数
    const date = new Date(rescreenTime.value);
    return "第" + getWeekNumber(date) + "周";
  });

  const weekArea = computed(() => {
    if (!rescreenTime.value) {
      return " ";
    }
    if (tableRef.value.getSelectionRows().length == 1) {
      return "";
    }

    let currentDate: any = new Date(rescreenTime.value);
    currentDate.setDate(currentDate.getDate() + 6);
    currentDate = dayjs(currentDate).format("YYYY/MM/DD");

    return `(${rescreenTime.value} - ${currentDate})`;
  });

  //获取数据的同时将数据展示
  function getData() {
    updataChart();
  }

  //初始化图表
  function initChart() {
    const chartDom1 = chartRef1.value;
    const chartDom2 = chartRef2.value;
    myChart1 = echarts.init(chartDom1);
    myChart2 = echarts.init(chartDom2);
    const option1 = {
      title: {
        show: true,
        text: "实际载货量/吨",
        textStyle: {
          color: "#a4c5e5",
        },
        top: 0,
        left: "30%",
      },
      tooltip: {
        show: true,
      },
      legend: {
        top: 20,
        textStyle: {
          color: "white",
        },
      },
      xAxis: {
        type: "category",
        data: ["星期一", "星期二", "星期三", "星期四", "星期五"],
      },
      yAxis: {
        type: "value",
      },
    };

    const option2 = {
      title: {
        show: true,
        text: "实际工作时长/小时",
        textStyle: {
          color: "#a4c5e5",
        },
        top: 0,
        left: "30%",
      },
      tooltip: {
        show: true,
      },
      legend: {
        top: 20,
        textStyle: {
          color: "white",
        },
      },
      xAxis: {
        type: "category",
        data: ["星期一", "星期二", "星期三", "星期四", "星期五"],
      },
      yAxis: {
        type: "value",
      },
    };
    myChart1.setOption(option1);
    myChart2.setOption(option2);
    //监听
    myChart1.on("legendselectchanged", function (params) {
      if (!transFromLegend.value) {
        transFromLegend.value = true; // 设置标志为 true
        myChart2.dispatchAction({
          type: "legendToggleSelect",
          name: params.name,
        });
        transFromLegend.value = false; // 事件处理完毕，重置标志
      }
    });

    myChart2.on("legendselectchanged", function (params) {
      if (!transFromLegend.value) {
        transFromLegend.value = true; // 设置标志为 true
        myChart1.dispatchAction({
          type: "legendToggleSelect",
          name: params.name,
        });
        transFromLegend.value = false; // 事件处理完毕，重置标志
      }
    });
  }

  function getWeekNum(week: string) {
    switch (week) {
      case "星期一":
        return 1;
      case "星期二":
        return 2;
      case "星期三":
        return 3;
      case "星期四":
        return 4;
      case "星期五":
        return 5;
      case "星期六":
        return 6;
      case "星期天":
        return 7;
    }
  }

  function isDateInWeek(dateString: string, weekStartString: string) {
    const date = new Date(dateString);
    const weekStart = new Date(weekStartString);

    // 获取周开始日期的时间戳
    const weekStartTimestamp = weekStart.getTime();

    // 计算周结束日期（周开始 + 6天）
    const weekEndTimestamp = weekStartTimestamp + 6 * 24 * 60 * 60 * 1000;

    // 判断日期是否在这周内
    return (
      date.getTime() >= weekStartTimestamp && date.getTime() <= weekEndTimestamp
    );
  }

  //更新图表
  function updataChart() {
    // if (analysisData.value.length < 1) {
    //   return;
    // }
    let chartOneSeriesArr = analysisData.value.map((item: any) => {
      const dataArr = [null, null, null, null, null];
      dataArr[getWeekNum(item.week) - 1] = item.actualLoad;
      return {
        name: item.licensePlateNumber,
        data: dataArr,
        type: "bar",
      };
    });
    let chartTwoSeriesArr = analysisData.value.map((item: any) => {
      const dataArr = [null, null, null, null, null];
      dataArr[getWeekNum(item.week) - 1] = item.actualTime;
      return {
        name: item.licensePlateNumber,
        data: dataArr,
        type: "bar",
      };
    });
    myChart1.setOption(
      {
        series: chartOneSeriesArr,
      },
      { replaceMerge: "series" }
    );
    myChart2.setOption(
      {
        series: chartTwoSeriesArr,
      },
      { replaceMerge: "series" }
    );
  }

  //确认分析
  function analysis() {
    if (rescreenTime.value === "") {
      ElMessage.error("未选日期");
      return;
    }
    console.log(rescreenTime.value);
    if (tableRef.value.getSelectionRows().length === 0) {
      ElMessage.error("没有勾选");
      return;
    }
    if (tableRef.value.getSelectionRows().length > 5) {
      ElMessage.error("不要勾选超过5辆");
      return;
    }
    //去重
    const carNameList = Array.from(
      new Set([
        ...tableRef.value
          .getSelectionRows()
          .slice(0, 5)
          .map((item: any) => {
            return item.licensePlateNumber;
          }),
      ])
    );
    console.log(carNameList);
    const test = store.actualCarTotalList.filter((item: any) => {
      return carNameList.includes(item.licensePlateNumber);
    });
    analysisData.value = test.filter((item: any) => {
      return isDateInWeek(item.date, rescreenTime.value);
    });
    console.log(analysisData.value);
    updataChart();
  }

  //打开搜索框
  function openSearch() {
    store.searchCarInfoData().then(() => {
      searchOpen.value = true;
    });
  }

  //关闭搜索框
  function closeSearch() {
    searchOpen.value = false;
  }

  //重新搜索
  function resetSearch() {
    searchFormData.value = {
      time: "",
      teamName: "",
      licensePlateNumber: "",
      carDriver: "",
    };
  }

  function confirmSearch() {
    if (
      !searchFormData.value.carDriver &&
      !searchFormData.value.licensePlateNumber &&
      !searchFormData.value.teamName &&
      !searchFormData.value.time
    ) {
      inSearch.value = false;
      searchResult.value = [];
      store.getAllCarData(searchData.pageSize).then(() => {
        getTableInitData(searchData.pageSize);
      });
      return;
    }
    searchResult.value = search(store.actualCarTotalList, searchFormData.value);
    searchData.pageNum = 1;
    actualList.value = searchResult.value.slice(
      searchData.pageNum * searchData.pageSize - searchData.pageSize,
      searchData.pageNum * searchData.pageSize
    );
    inSearch.value = true;
  }

  function search(data: any, searchParams: any) {
    return data.filter((item: any) => {
      // 根据搜索条件进行匹配
      const licensePlateNumber = searchParams.licensePlateNumber
        ? item.licensePlateNumber === searchParams.licensePlateNumber
        : true;
      const matchesTeam = searchParams.teamName
        ? item.teamName === searchParams.teamName
        : true;
      const matchesDriver = searchParams.carDriver
        ? item.carDriverName === searchParams.carDriver
        : true;
      const matchesTime = searchParams.time
        ? item.date && item.date === searchParams.time
        : true;

      return licensePlateNumber && matchesTeam && matchesDriver && matchesTime;
    });
  }

  //打开添加框
  function openAdd() {
    if (inSearch.value) {
      ElMessage({
        message: "还在搜索状态中,禁止打开弹窗",
        type: "warning",
      });
      return;
    }
    store.getActualDownBox().then(() => {
      addActualRef.value.addActualOpen = true;
    });
  }

  function confirmAdd(data: any) {
    if (
      store.actualCarTotalList.findIndex((item: any) => {
        return (
          (item.licensePlateNumber === data.licensePlateNumber &&
            item.date === data.date) ||
          (item.licensePlateNumber === data.licensePlateNumber &&
            item.routeName === data.route)
        );
      }) !== -1
    ) {
      ElMessage({
        message: "含重复项!",
        type: "warning",
      });
      return;
    }
    store.addCarActual(data).then((res) => {
      if (res.code === 50001 || res.message === "系统异常") {
        ElMessage.error("系统异常!");
        return;
      }
      store.getTotalCarActual(searchData.pageSize).then(() => {
        pageQuery(searchData.pageNum, searchData.pageSize);
        ElMessage({
          message: "添加成功!",
          type: "success",
        });
      });
    });
  }

  //打开删除框
  function openDelete() {
    if (inSearch.value) {
      ElMessage({
        message: "还在搜索状态中,禁止打开弹窗",
        type: "warning",
      });
      return;
    }
    if (tableRef.value.getSelectionRows().length !== 1) {
      ElMessage({
        message: "请选择一个实情删除!",
        type: "warning",
      });
      return;
    }
    deleteActualRef.value.deleteVis = true;
  }

  function confirmDelete() {
    deleteActualRef.value.deleteVis = false;
    store.getTotalCarActual(searchData.pageSize).then(() => {
      pageQuery(searchData.pageNum, searchData.pageSize);
      ElMessage({
        message: "删除成功!",
        type: "success",
      });
    });
  }

  //打开修改框
  function openChange() {
    if (inSearch.value) {
      ElMessage({
        message: "还在搜索状态中,禁止打开弹窗",
        type: "warning",
      });
      return;
    }
    if (tableRef.value.getSelectionRows().length !== 1) {
      ElMessage({
        message: "请单选一个实情修改",
        type: "warning",
      });
      return;
    }
    changeActualRef.value.changeActualOpen = true;
  }

  const current = ref<number>(0);
  function confirmChange() {
    current.value = searchData.pageNum;
    changeActualRef.value.changeActualOpen = false;
    store.getTotalCarActual(searchData.pageSize).then(() => {
      searchData.pageNum = current.value;
      pageQuery(searchData.pageNum, searchData.pageSize);
      ElMessage({
        message: "修改成功!",
        type: "success",
      });
    });
  }

  //打开导入表格
  function openUpload() {
    uploadActualRef.value.uploadVisible = true;
  }

  //打开导入日记
  function openNote() {
    store.noteData = { total: "0" };
    nextTick(() => {
      noteRef.value.noteVisible = true;
      noteRef.value.onOpenDialog();
    });
  }

  const info = ref<any>();
  //处理选择行
  function handleSelect(rowData: any) {
    if (rowData.length == 1) {
      shouldShowSelectedDate.value = false; // 重新选择行时，重置显示标志
    } else {
      shouldShowSelectedDate.value = false; // 选择多行时，也重置显示标志
    }
    if (rowData.length >= 1) {
      info.value = Object.assign({}, rowData[0]);
    }
    // changeWeek();
  }

  //处理分页
  function handlePageChange(num: number = 1) {
    searchData.pageNum = num;
    if (inSearch.value) {
      actualList.value = searchResult.value.slice(
        searchData.pageNum * searchData.pageSize - searchData.pageSize,
        searchData.pageNum * searchData.pageSize
      );
      return;
    }
    pageQuery(searchData.pageNum, searchData.pageSize);
  }

  //传入分页参数
  function pageQuery(pageNum: number, pageSize: number) {
    if (inSearch.value) {
      confirmSearch();
      return;
    }
    actualList.value = store.actualCarTotalList.slice(
      pageNum * pageSize - pageSize,
      pageNum * pageSize
    );
  }

  //获取初始表格数据
  function getTableInitData(pageSize: number) {
    actualList.value = store.actualCarTotalList.slice(0, pageSize);
  }

  function Nindex(index: number) {
    const page = searchData.pageNum; // 当前页码
    const pagesize = searchData.pageSize; // 每页条数
    return index + 1 + (page - 1) * pagesize;
  }
</script>

<style lang="less" scoped>
  .frame1 {
    // :deep(.el-button) {
    //   color: #7c9dcb;
    //   border: 2px solid #7c9dcb !important;
    // }
    display: flex;
    justify-content: center;
    width: 80vw;
    height: 32vh;
    .work {
      width: 90%;
      height: 90%;
      background-color: #011631;
      border: 1px solid #90ade8;
      margin-top: 30px;
      display: flex;
      flex-direction: column;
      align-items: center;
      .title {
        margin-left: -20%;
        font-size: 20px;
        margin-top: 10px;
      }
      .echarts {
        display: flex;
        width: 100%;
        height: 100%;

        .echart1 {
          width: 40%;
          height: 100%;
        }
        .echart2 {
          width: 40%;
          height: 100%;
          margin-left: 10px;
        }
        .button {
          display: flex;
          flex-direction: column;
          margin-left: 60px;
          width: 9%;
          height: 100%;
          justify-content: center;
          position: relative;
          :deep(.el-date-editor) {
            width: 6.8vw;
            height: 4.5vh !important;
            // border: 2px solid #7c9dcb !important;
            border: 0;
            background-color: #052658;
            color: #7c9dcb !important;
            font-size: 14px;
          }
          :deep(.el-input__inner::placeholder) {
            border: 0;
            color: #7c9dcb;
            font-size: 14px;
            text-align: center;
          }
          :deep(.el-input__prefix) {
            display: none;
          }
        }
      }
    }
  }
  .frame2 {
    // :deep(.el-button) {
    //   color: #7c9dcb;
    //   border: 2px solid #7c9dcb !important;
    // }
    margin-top: 0.2vh;
    width: 80vw;
    height: 47vh;
    display: flex;
    justify-content: center;
    .work {
      width: 95%;
      height: 100%;
      display: flex;
      flex-direction: column;
      .toolbox {
        .input {
          width: 400px;
          margin-left: 20px;
          margin-top: 10px;
        }
        .button {
          margin-left: 10px;
          width: max-content;
          height: 30px;
          margin-top: 10px;
        }

        .top {
          margin: 1vh 0px;

          .select {
            margin: 0 1vw;
          }

          .btn {
            box-sizing: border-box;
            width: max-content;
            // width: 2.3vw;
            height: 4vh;
          }

          .search {
            position: absolute;
            z-index: 9;
            background-color: #091b3a;
            width: 500px;

            .off {
              width: 2vw;
              height: 3vh;
              position: absolute;
              right: 0;
              color: white;
              text-align: center;
              cursor: pointer;
            }

            .form {
              display: flex;
              flex-wrap: wrap;
              padding: 20px;
              align-items: center;
              border: 1px solid #9addf6;
            }

            .btn-content {
              margin: 0 auto;
            }

            .el-form-item {
              width: 33.3%;
            }
          }
        }

        .second {
          margin: 0.5vh;
          .btn {
            margin: 0 10px;
            box-sizing: border-box;
            width: 6.5vw;
            height: 4vh;
          }
        }
      }
    }
  }

  .pageDivide {
    margin-top: 5vh;
    display: flex;
    justify-content: center;
  }

  :deep(.el-pagination) {
    margin-bottom: 0;
  }
</style>
