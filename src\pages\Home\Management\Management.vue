<template>
  <div class="Management">
    <div class="List">
      <div
        class="listItem"
        v-for="(item, index) in ManagementList"
        :class="{ active: currentRoute === item.value }"
        :key="index"
        @click="routerChange(item)"
      >
        {{ item.name }}
      </div>
    </div>
    <div class="content">
      <router-view></router-view>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { useRouter, useRoute } from "vue-router";
  const router = useRouter();
  const routes = useRoute();
  import { ManagementList } from "../config";
  const currentRoute = computed(() => {
    if (!routes.path.includes("/management")) {
      return "";
    }
    if (routes.path.includes("/work")) {
      return "message";
    }
    // console.log(ManagementList.find((item: any) => routes.path == item.router))
    return ManagementList.find((item: any) => routes.path == item.router).value;
  });
  function routerChange(route: any) {
    router.push(route.router);
    console.log(routes);
    currentRoute.value = route.value;
  }

  onUpdated(() => {});
</script>
<style lang="scss" scoped>
  .Management {
    display: flex;

    .List {
      width: 10vw;
      height: 70vh;
      color: white;
      font-size: 1.2vw;
      text-align: center;
      margin-right: 2vw;
      .listItem {
        cursor: pointer;
        padding: 1vw 0;
        // background-color: #84aeeb;
      }
      .active {
        background-color: #84aeeb;
      }
    }
    .content {
      width: 100%;
    }
  }
</style>
