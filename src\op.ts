import { App, DirectiveBinding } from "vue";

export default (app: App) => {
  app.directive("op", {
    mounted(el: HTMLElement, binding: DirectiveBinding) {
      if (!hasOp(binding.value)) {
        el.style.display = "none";
      }
    },
  });
};

export function hasOp(op: string) {
  try {
    const operationData = localStorage.getItem("operation");
    if (!operationData || operationData === "null" || operationData === "undefined") {
      return false;
    }
    const operation: string[] = JSON.parse(operationData);
    return Array.isArray(operation) && operation.includes(op);
  } catch (error) {
    console.error("Error parsing operation data from localStorage:", error);
    return false;
  }
}
