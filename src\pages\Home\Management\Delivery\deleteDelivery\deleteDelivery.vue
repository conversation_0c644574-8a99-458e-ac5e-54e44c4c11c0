<template>
    <el-dialog
      v-model="deleteVis"
      title="删除配送域"
      class="transform"
      width="40%"
      align-center
      :close-on-click-modal="false"
    >
      <div class="content">确定删除所选配送域</div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelDialog" type="primary">取消</el-button>
          <el-button type="primary" @click="confirmDialog">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </template>
  
  <script setup lang="ts">
  import { IDeliveryDeleteList } from '@/types/delivery';
  import { useDeliveryStore } from '@/store/delivery';
  const store = useDeliveryStore()
  const deleteVis = ref<boolean>(false)
  const deleteEmit = defineEmits(["confirmDelete"]);
  const props = defineProps(["id"])
  
  
  defineExpose({
  deleteVis
  });
  
  function cancelDialog() {
  deleteVis.value = false
  }
  
  function confirmDialog() {
    const data : IDeliveryDeleteList = {
      id: Number(props.id)
    }
    store.deleteDelivery(data).then((res)=> {
      if(res.code === 50001 || res.message === '系统异常') {
        ElMessage.error('系统异常!')
        return
      }
      if(res.includes('删除失败')) {
        ElMessage.error(res)
        deleteVis.value = false
        return
      }
      deleteEmit("confirmDelete")
    })
  }
  
  </script>
  
  <style lang="less" scoped>
  .transform {
    .content {
      display: flex;
      justify-content: center;
      font-size: 20px;
    }
  }
  </style>
  