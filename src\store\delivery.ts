import { defineStore } from "pinia";
import type {
  IDeliveryGetList,
  IDeliveryData,
  IDeliveryAddList,
  IDeliveryD<PERSON><PERSON><PERSON>ist,
  IDeliveryUpdateList,
} from "@/types/delivery";

import {
  getDeliveryList,
  getDeliverySelectListData,
  addDeliveryData,
  updateDeliveryData,
  deleteDeliveryData,
  getDeliveryAddInfo,
} from "@/service/modules/delivery";

export const useDeliveryStore = defineStore("delivery", () => {
  const deliveryList = ref<IDeliveryData[]>([]);
  const deliveryTotalList = ref<any>([]);
  const selectList = ref<any>();
  const info = ref<any>();

  async function getDeliveryData(params: IDeliveryGetList) {
    const res: any = await getDeliveryList(params);
    if (
      deliveryList.value.findIndex(
        (item) => item.deliveryId === res.data.records[0].deliveryId
      ) === -1
    ) {
      deliveryList.value.push(res.data.records[0]);
    }
  }

  //获得全部配送域
  async function getTotalDelivery(pageSize: number) {
    let pageNum = 1;
    let hasMoreData = true;
    deliveryTotalList.value = [];
    while (hasMoreData) {
      try {
        const res: any = await getDeliveryList({ pageNum, pageSize });
        deliveryTotalList.value = [
          ...deliveryTotalList.value,
          ...res.data.records,
        ];
        hasMoreData = res.data.records.length === pageSize;
        pageNum++;
      } catch (error) {
        console.error("请求数据时出错:", error);
        hasMoreData = false; // 出现错误时停止请求
      }
    }
  }

  //修改配送域
  async function updateDelivery(params: IDeliveryUpdateList) {
    const res = await updateDeliveryData(params);
    return res;
  }

  //删除配送域
  async function deleteDelivery(params: IDeliveryDeleteList) {
    const res: any = await deleteDeliveryData(params);
    return res;
  }

  //添加配送域
  async function addDelivery(params: IDeliveryAddList) {
    const res = await addDeliveryData(params);
    return res;
  }

  //获取下拉框信息
  async function getDeliverySelectList() {
    const res: any = await getDeliverySelectListData();
    selectList.value = res.data;
  }

  async function getDeliveryAddDownBox() {
    const res: any = await getDeliveryAddInfo();
    info.value = res.data;
  }

  return {
    getDeliveryData,
    getDeliverySelectList,
    getTotalDelivery,
    addDelivery,
    updateDelivery,
    deleteDelivery,
    getDeliveryAddDownBox,
    deliveryList,
    deliveryTotalList,
    selectList,
    info,
  };
});
