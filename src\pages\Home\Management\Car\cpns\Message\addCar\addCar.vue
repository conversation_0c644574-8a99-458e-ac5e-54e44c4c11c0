<template>
    <div class="addCarDialog">
      <el-dialog v-model="addCarOpen" title="添加车辆信息" width="60%" height="70%" :close-on-click-modal="false" @close="closeAdd">
        <div class="flex-center">
          <el-form label-width="auto" class="areaForm" :model="totalFields" ref="formRef" :rules="rules">
          <div class="flex">
            <el-form-item label="车牌号" prop="licensePlateNumber">
              <el-input
                style="width: 250px;"
                placeholder="点击输入"
                v-model="totalFields.licensePlateNumber"
              >
              </el-input>
          </el-form-item>
          <el-form-item label="配送域" prop="deliveryAreaName">
            <el-select
              style="width: 250px;"
              placeholder="请选择"
              v-model="totalFields.deliveryAreaName"
            >
            <el-option :label="item" :value="item" v-for="(item) in store.carAddInfo.deliveryList" :key="item +'9'"/>
            </el-select>
          </el-form-item>
          </div>
          <div class="flex">
            <el-form-item label="驾驶人" prop="carDriverName">
            <el-select
              style="width: 250px;"
              placeholder="请选择"
              v-model="totalFields.carDriverName"
              @change="completePhone"
            >
            <el-option :label="item.userName" :value="item.userName" v-for="(item) in store.carAddInfo.carDriver" :key="item +'9'"/>
            </el-select>
          </el-form-item>
          <el-form-item label="电话" prop="carDriverPhone">
            <el-select
              style="width: 250px;"
              placeholder="请选择"
              v-model="totalFields.carDriverPhone"
              @change="completeDriver"
            >
            <el-option :label="item.phone" :value="item.phone" v-for="(item) in store.carAddInfo.carDriver" :key="item +'9'"/>
            </el-select>
          </el-form-item>
          </div>
          <div class="flex">
            <el-form-item label="最大载重" prop="maxLoad" :style="{display: 'flex'}">
              <el-input
                style="width: 200px;"
                placeholder="请输入"
                v-model="totalFields.maxLoad"
              >
              </el-input>
              <div class="tons">吨</div>
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select
              style="width: 250px;"
              placeholder="请选择"
              v-model="totalFields.status"
            >
            <el-option :label="item" :value="item" v-for="(item) in store.carAddInfo.status" :key="item +'9'"/>
            </el-select>
          </el-form-item>
          </div>
        </el-form>
        </div>
  
        <div class="btns">
          <el-button type="primary" @click="closeAdd">取消</el-button>
          <el-button type="primary" style="margin-left: 100px" @click="confirmAdd"
            >确定</el-button
          >
        </div>
      </el-dialog>
    </div>
</template>
  
<script setup lang="ts">
  import { carStore } from "@/store/managerment/car";
  const store = carStore()
  const addCarOpen = ref(false);
  defineExpose({
    addCarOpen
  });
  const formRef = ref<any>()
  const addEmit = defineEmits(["confirmAdd"]);
  const totalFields = ref<any>({
    licensePlateNumber: '',
    carDriverName: '',
    carDriverPhone: '',
    deliveryAreaName: '',
    maxLoad: '',
    status: '',
  })

  
const validateMaxLoad = (rule: any, value: any, callback: any) => {
  if (value === '') {
    callback(new Error('请输入吨数'));
  } else if (isNaN(value) || value.trim() === '') {
    callback(new Error('请输入数字'));
  } else if (Number(value) > 2) {
    callback(new Error('不能超过2吨'));
  } else {
    callback(); // 验证通过
  }
}

const rules = reactive({
  licensePlateNumber: [
    { required: true, message: '请输入', trigger: 'blur' },
  ],
  carDriverName: [
    {
      required: true,
      message: '请选择',
      trigger: 'change',
    },
  ],
  status: [
    {
      required: true,
      message: '请选择',
      trigger: 'change',
    },
  ],
  maxLoad: [
    {
      validator: validateMaxLoad, trigger: 'blur'
    },
  ],
})


  //确认添加
  async function confirmAdd() {
    if (!formRef.value) return
    formRef.value.validate((valid : any) => {
    if (valid) {
      addEmit("confirmAdd", totalFields.value);
      formRef.value.resetFields()
    } else {
      ElMessage({
        message: '添加失败!',
        type: 'warning',
      })
    }
  })
  }
  
  //取消添加
  const closeAdd = () => {
    formRef.value.resetFields()
    addCarOpen.value = false;
  };

  function completeDriver() {
    totalFields.value.carDriverName = store.carAddInfo.carDriver.find((item: any) => 
      item.phone === totalFields.value.carDriverPhone
    ).userName
  }

  function completePhone() {
    totalFields.value.carDriverPhone = store.carAddInfo.carDriver.find((item: any) => 
      item.userName === totalFields.value.carDriverName
    ).phone
  }

</script>
  
<style lang="less" scoped>
.flex-center {
  width: 100%;
  display: flex;
  justify-content: center;
}
.flex {
  display: flex;
}
  .areaForm {
    .areaItem {
      font-size: 20px;
    }
  }
  .btns {
    display: flex;
    justify-content: center;
    color: black;
  }
  
  .transform {
    .content {
      display: flex;
      justify-content: center;
      flex-wrap: wrap;
      font-size: 20px;
    }
  }
  
  .tons {
    font-size: 20px;
    margin-left: 20px;
  }
</style>
  