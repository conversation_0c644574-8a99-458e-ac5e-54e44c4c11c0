import { BASE_URL, TIME_OUT } from "./config";
import request from "./request";
const requests = new request({
  baseURL: BASE_URL,
  timeout: TIME_OUT,
  interceptors: {
    requestSuccessFn: (config) => {
      const token = localStorage.getItem("token");
      if (config.headers && token) {
        config.headers.Authorization = token;
      }
      return config;
    },
    responseSuccessFn: (response: any) => {
      if (response.code === 500) {
        ElMessage.error(response.msg);
      } else if (response.code === 403) {
        // 权限不足
        ElMessage.error(response.msg || "权限不足，无法执行此操作");
      } else if (response.code === 401) {
        // 登录过期或无权限
        ElMessage.error(response.msg || "登录已过期，请重新登录");
        // 可以在这里添加跳转到登录页的逻辑
        // router.push('/login');
      }
      return response;
    },
  },
});

export default requests;
