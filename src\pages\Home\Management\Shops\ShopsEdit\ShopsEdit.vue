<template>
  <div class="ShopsEdit">
    <el-dialog
      :title="title"
      v-model="ShopsEditIsOpen"
      width="80%"
      @close="closeShopsEdit(formRef)"
      @open="openShops"
    >
      <div>
        <div class="ShopsEditInfo">
          <el-form
            class="form"
            ref="formRef"
            :model="searchForm"
            :rules="rules"
          >
            <div class="leftInfo">
              <el-form-item prop="customerCode">
                <div class="info">客户编码：{{ searchForm.customerCode }}</div>
              </el-form-item>
              <el-form-item label="地址" prop="storeAddress">
                <el-input
                  placeholder="点击输入"
                  v-model="searchForm.storeAddress"
                ></el-input>
              </el-form-item>
              <el-form-item label="商铺经度" prop="longitude">
                <el-input
                  placeholder="点击输入"
                  v-model="searchForm.longitude"
                ></el-input>
              </el-form-item>
              <el-form-item label="所属大区" prop="areaName">
                <el-select
                  class="select"
                  v-model="searchForm.areaName"
                  placeholder="全部区域"
                  size="large"
                >
                  <el-option
                    v-for="item in areas"
                    :key="item.areaId"
                    :label="item.areaName"
                    :value="item.areaName"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="负责人" prop="head">
                <el-input
                  placeholder="点击输入"
                  v-model="searchForm.head"
                ></el-input>
              </el-form-item>
              <el-form-item label="订货电话" prop="contactPhone">
                <el-input
                  placeholder="点击输入"
                  v-model="searchForm.contactPhone"
                ></el-input>
              </el-form-item>
              <div class="info" v-if="title.includes('修改')">
                订货周期：{{ searchForm.orderCycle }}
              </div>

              <el-form-item prop="createTime" v-if="title.includes('修改')">
                <!-- <div class="info" prop="customerCode"> -->
                <div class="info">创建时间：{{ searchForm.createTime }}</div>
              </el-form-item>
            </div>
            <div class="centerInfo">
              <el-form-item label="客户名称" prop="contactName">
                <el-input
                  placeholder="点击输入"
                  v-model="searchForm.contactName"
                ></el-input>
              </el-form-item>
              <el-form-item
                prop="accumulationName"
                v-if="title.includes('修改')"
              >
                <div class="info">
                  所属打卡点：{{ searchForm.accumulationName }}
                </div>
              </el-form-item>
              <el-form-item label="商铺维度" prop="latitude">
                <el-input
                  placeholder="点击输入"
                  v-model="searchForm.latitude"
                ></el-input>
              </el-form-item>
              <el-form-item label="客户状态" prop="status">
                <el-select v-model="searchForm.status"
                  ><el-option label="异常" value="0" />
                  <el-option label="正常" value="1" />
                </el-select>
              </el-form-item>
              <el-form-item label="收货电话" prop="receivingPhone">
                <el-input
                  placeholder="点击输入"
                  v-model:model-value="searchForm.receivingPhone"
                ></el-input>
              </el-form-item>
              <el-form-item
                label="返销周期"
                v-if="title.includes('添加')"
                prop="resaleCycle"
              >
                <el-input
                  placeholder="点击输入"
                  v-model:model-value="searchForm.resaleCycle"
                ></el-input>
              </el-form-item>
              <el-form-item label="客户专员名称" prop="customerManagerName">
                <el-input
                  placeholder="点击输入"
                  v-model="searchForm.customerManagerName"
                ></el-input>
              </el-form-item>
              <el-form-item prop="updateTime" v-if="title.includes('修改')">
                <div class="info">更新时间：{{ searchForm.updateTime }}</div>
              </el-form-item>
            </div>
            <div class="rightInfo">
              <el-form-item label="商圈类型" prop="type">
                <el-select v-model="searchForm.type">
                  <el-option
                    v-for="item in types"
                    :key="item"
                    :label="item"
                    :value="item"
                /></el-select>
              </el-form-item>
              <el-form-item label="商铺类型" prop="locationType">
                <el-select v-model="searchForm.locationType">
                  <el-option
                    v-for="item in locationTypes"
                    :key="item"
                    :label="item"
                    :value="item"
                /></el-select>
              </el-form-item>
              <el-form-item label="所属配送路线 :" prop="routeName">
                <el-input
                  style="width: 160px"
                  placeholder="点击输入"
                  v-model:model-value="searchForm.routeName"
                ></el-input>
              </el-form-item>
              <el-form-item label="客户档位" prop="gear">
                <el-select v-model="searchForm.gear"
                  ><el-option
                    v-for="item in gears"
                    :key="item"
                    :label="item"
                    :value="item"
                /></el-select>
              </el-form-item>
              <el-form-item label="备用收货电话" prop="sparePhone">
                <el-input
                  placeholder="点击输入"
                  v-model:model-value="searchForm.sparePhone"
                ></el-input>
              </el-form-item>
              <el-form-item label="是否为特殊点" prop="isSpecial">
                <el-select v-model="searchForm.isSpecial"
                  ><el-option label="是" value="1" /><el-option
                    label="否"
                    value="0"
                /></el-select>
              </el-form-item>
            </div>
          </el-form>
        </div>
        <div class="ShopsEditBtn">
          <el-button @click="closeShopsEdit(formRef)">取消</el-button>
          <el-button @click="updateStore">确认</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>
  import { usedatamanagementStore } from "@/store/datamanagement";
  const datamanagementStore = usedatamanagementStore();
  const update = defineEmits(["update"]);
  import type { FormInstance } from "element-plus";
  const title = ref<string>("");
  const storeId = ref<number>(0);
  const formRef = ref<FormInstance>();
  const ShopsEditIsOpen = ref<boolean>(false);
  const areas = ref<any[]>();
  const searchForm = ref<any>({
    areaName: "",
    receivingPhone: "",
    contactName: "",
    contactPhone: "",
    createTime: "",
    customerCode: "",
    customerManagerName: "",
    accumulationName: "",
    resaleCycle: "",
    district: "",
    gear: "",
    orderCycle: "",
    routeName: "",
    status: "",
    storeAddress: "",
    storeName: "",
    type: "",
    updateTime: "",
    locationType: "",
    head: "",
    sparePhone: "",
    isSpecial: "",
  });

  const rules = reactive({
    storeAddress: [{ required: true, message: "请输入", trigger: "blur" }],
    routeName: [{ required: true, message: "请输入", trigger: "blur" }],
    customerManagerName: [
      { required: true, message: "请输入", trigger: "blur" },
    ],
    longitude: [
      {
        required: true,
        message: "请选择",
        trigger: "change",
      },
    ],
    latitude: [
      {
        required: true,
        message: "请选择",
        trigger: "change",
      },
    ],
    areaName: [
      {
        required: true,
        message: "请选择",
        trigger: "change",
      },
    ],
    status: [
      {
        required: true,
        message: "请选择",
        trigger: "change",
      },
    ],
    locationType: [
      {
        required: true,
        message: "请选择",
        trigger: "change",
      },
    ],
    gear: [
      {
        required: true,
        message: "请选择",
        trigger: "change",
      },
    ],
  });

  const types: string[] = [
    "商业娱乐区",
    "旅游景区",
    "居民区",
    "其他",
    "工业区",
    "院校学区",
    "办公区",
    "交通枢纽区",
    "集贸区",
    "农林渔牧区",
  ];
  const gears: string[] = [
    "一档",
    "二档",
    "三档",
    "四档",
    "五档",
    "六档",
    "七档",
    "八档",
    "九档",
    "十档",
    "十一档",
    "十二档",
    "十三档",
    "十四档",
    "十五档",
    "十六档",
    "十七档",
    "十八档",
    "十九档",
    "二十档",
    "二十一档",
    "二十二档",
    "二十三档",
    "二十四档",
    "二十五档",
    "二十六档",
    "二十七档",
    "二十八档",
    "二十九档",
    "三十档",
  ];
  const locationTypes: string[] = ["乡镇", "城区"];
  const closeShopsEdit = (formEl: FormInstance | undefined) => {
    if (!formEl) return;
    formEl.resetFields();
    ShopsEditIsOpen.value = false;
  };
  function openShops() {
    if (title.value == "修改商铺信息") {
      datamanagementStore.getStoreDetialAction(storeId.value).then(() => {
        searchForm.value = datamanagementStore.storeDetialData;
      });
    } else {
      datamanagementStore.getAddCode().then((res) => {
        searchForm.value.customerCode = res.data.customerCode;
      });
    }
  }
  function updateStore() {
    if (!formRef.value) return;
    formRef.value.validate((valid: any) => {
      if (valid) {
        if (title.value == "修改商铺信息") {
          datamanagementStore.updateStoreAction(searchForm.value).then(() => {
            update("update");
          });

          ShopsEditIsOpen.value = false;
        } else {
          const areaId = areas.value?.filter(
            (item) => item.areaName == searchForm.value.areaName
          )[0].areaId;
          datamanagementStore
            .addStoreAction(searchForm.value, areaId)
            .then(() => {
              update("update");
            });
          ShopsEditIsOpen.value = false;
        }
      } else {
        ElMessage({
          message: "更改失败!",
          type: "warning",
        });
      }
    });
  }
  defineExpose({
    ShopsEditIsOpen,
    title,
    storeId,
    areas,
  });

  onMounted(() => {
    datamanagementStore.getAddCode().then((res) => {
      console.log((searchForm.value.customerCode = res.data.customerCode));
      searchForm.value.customerCode = res.data.customerCode;
    });
  });
</script>
<style lang="scss" scoped>
  .ShopsEdit {
    margin: 25px auto;
    width: 48vw;
    color: #73e1ff;
    font-size: 20px;

    .form {
      display: flex;

      .info {
        height: 45px;
        font-size: 20px;
        color: rgb(204, 255, 255);
      }

      .leftInfo {
        width: 30%;
        margin-left: 30px;
      }

      .centerInfo {
        width: 30%;
        margin-left: 50px;
      }

      .rightInfo {
        width: 30%;
        margin-left: 50px;
      }
    }

    .ShopsEditBtn {
      display: flex;
      justify-content: center;

      .el-button {
        width: 85px;
        margin: 30px 5vw;
      }
    }
  }
</style>
