<template>
  <div class="add">
    <el-dialog
      v-model="addPostOpen"
      :modal="false"
      title="添加选址"
      class="flex"
    >
      <el-form
        label-width="auto"
        width="100%"
        :inline="true"
        class="areaForm"
        :model="data"
        ref="formRef"
        :rules="formRules"
      >
        <el-form-item label="名称" prop="pickupName">
          <el-input placeholder="请输入" v-model="data.pickupName"></el-input>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select placeholder="请选择" v-model="data.status">
            <el-option label="启用" :value="2" />
            <el-option label="禁用" :value="1" />
          </el-select>
        </el-form-item>
        <el-form-item label="经度" prop="longitude">
          <el-input placeholder="请输入" v-model="data.longitude"></el-input>
        </el-form-item>
        <el-form-item label="纬度" prop="latitude">
          <el-input placeholder="请输入" v-model="data.latitude"></el-input>
        </el-form-item>
        <el-form-item label="取货地类型" prop="type">
          <el-select placeholder="请选择" v-model="data.type">
            <el-option label="邮局" value="邮局" />
            <el-option label="烟站" value="烟站" />
            <el-option label="村委会" value="村委会" />
            <el-option label="其他" value="其他" />
          </el-select>
        </el-form-item>
        <el-form-item label="详细地址" prop="pickupAddress">
          <el-input
            placeholder="请输入"
            v-model="data.pickupAddress"
          ></el-input>
        </el-form-item>
      </el-form>
      <div class="btns">
        <el-button type="primary" @click="closeAdd">取消</el-button>
        <el-button type="primary" style="margin-left: 100px" @click="confirmAdd"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  const addPostOpen = ref<boolean>(false);

  defineExpose({ addPostOpen });
  const add = defineEmits(["add"]);
  import { reactive } from "vue";

  const data = reactive({
    pickupName: "",
    status: "",
    longitude: "",
    latitude: "",
    type: "",
    pickupAddress: "",
  });

  const formRef = ref<any>();
  const formRules = reactive({
    pickupName: [{ required: true, message: "不能为空", trigger: "blur" }],
    status: [{ required: true, message: "不能为空", trigger: "blur" }],
    longitude: [{ required: true, message: "不能为空", trigger: "blur" }],
    latitude: [{ required: true, message: "不能为空", trigger: "blur" }],
    type: [{ required: true, message: "不能为空", trigger: "blur" }],
    pickupAddress: [{ required: true, message: "不能为空", trigger: "blur" }],
  });
  const closeAdd = () => {
    formRef.value.resetFields();
    addPostOpen.value = false;
  };
  const confirmAdd = () => {
    formRef.value.validate((valid: boolean) => {
      if (valid) {
        add("add", data);
        addPostOpen.value = false;
      } else {
        ElMessage({
          type: "warning",
          message: "请输入正确的信息",
        });
      }
    });
  };
</script>

<style scoped lang="less">
  :deep(.el-dialog) {
    .areaForm {
      text-align: center;
    }
  }
  .btns {
    display: flex;
    justify-content: center;
    color: black;
  }
</style>
