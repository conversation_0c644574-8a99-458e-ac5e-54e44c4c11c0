<template>
  <div class="addAreaDialog">
    <el-dialog v-model="addAreaOpen" title="添加班组信息" width="60%" :close-on-click-modal="false" @close="closeAdd">
      <el-form label-width="auto" class="areaForm" :model="formData" :rules="rules" ref="formRef">
        <el-form-item label="班组" prop="teamName">
          <div class="areaItem"> {{ props.teamName }}</div>
        </el-form-item>
        <el-form-item label="配送域" prop="deliveryList">
          <el-select
            style="width: 440px"
            multiple
            placeholder="请选择"
            v-model="formData.deliveryList"
            @change="changeDelivery"
          >
          <el-option label="无" value="无" />
          <el-option v-for="item in deliveryStore.selectList.deliveryList" :label="item" :value="item" :key="item + '2'"/>
          </el-select>
        </el-form-item>
        <el-form-item label="中转站" prop="transitDepotFromData">
          <div class="flex">
            <el-tag type="primary" v-for="(item) in formData.transitDepotFromData" :key="item + '2'">{{ item }}</el-tag>
          </div>
        </el-form-item>
        <el-form-item label="车辆总数" prop="carSum">
          <div class="areaItem"> {{ formData.carSum }}</div>
        </el-form-item>
        <el-form-item label="路径总数" prop="routeSum">
          <div class="areaItem">
            {{ formData.routeSum }}
          </div>
        </el-form-item>
      </el-form>

      <div class="btns">
        <el-button type="primary" @click="closeAdd">取消</el-button>
        <el-button type="primary" style="margin-left: 100px" @click="confirmAdd"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
  <el-dialog
    v-model="confirmDialogVis"
    title="确认变更"
    class="transform"
    width="40%"
    align-center
    :close-on-click-modal="false"
  >
    <div class="content">{{ transformText }}</div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancelDialog" type="primary">取消</el-button>
        <el-button type="primary" @click="confirmDialog">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { useAreaStore } from "@/store/managerment/area";
import { useDeliveryStore } from '@/store/delivery';
const props = defineProps(['teamName'])
const formData = ref<any>({
  teamName: props.teamName,
  deliveryList: [],
  carSum: 0,
  routeSum: 0,
  transitDepotFromData: []
})
const rules = reactive({
  deliveryList: [
    { required: true, message: '请选择', trigger: 'change' },
  ]
})
const formRef = ref<any>()
const areaStore = useAreaStore()
const deliveryStore = useDeliveryStore()
const addAreaOpen = ref(false);
defineExpose({
  addAreaOpen,
});


//弹窗的可视化
const confirmDialogVis = ref<boolean>(false)

//仓库配送域数据列表
const addEmit = defineEmits(["confirmAdd"]);
const transformDialogTotalData = ref<any>({})

//第一次确认
async function opentransformDiaLog() {
  addAreaOpen.value = false
  transformDialogTotalData.value = await getTeamAddDataTransfROM()
  areaStore.queryAreaAddData(transformDialogTotalData.value).then((res) => {
    transformText.value = res;
  })
  confirmDialogVis.value = true
}

const teamInfo = computed(() => areaStore.addTeamInfo)

//确定添加班组
async function confirmAdd() {
  if (!formRef.value) return
  await formRef.value.validate((valid : any) => {
    if (valid) {
      opentransformDiaLog()//打开弹窗
    } else {
      ElMessage({
      message: '添加失败',
      type: 'warning'
      })
    }
  })
}

//取消添加班组
const closeAdd = () => {
  formRef.value.resetFields()
  addAreaOpen.value = false;
};

const transformText = ref<string>('')

function confirmDialog() {
  confirmDialogVis.value = false;
  areaStore.addAreaData(transformDialogTotalData.value).then((res) => {
    formRef.value.resetFields()
    if(res.code === 50001 || res.message === '系统异常') {
      ElMessage.error('系统异常!')
      return
    }
    addEmit("confirmAdd", transformText.value.includes(`${props.teamName}配送域为[]`))
  })
}

//取消变更
function cancelDialog() {
  formRef.value.resetFields()
  confirmDialogVis.value = false;
}

function changeDelivery() {
  if(formData.value.deliveryList.includes("无")) {
    formData.value.deliveryList = ["无"]
    formData.value.carSum = 0
    formData.value.routeSum = 0
    formData.value.transitDepotFromData = []
    return
  }
  areaStore.getAddTeamInfo().then(() => {
    formData.value.transitDepotFromData = []
    const deliveryArray = 
    teamInfo.value.deliveryList.filter((item : any) => 
    formData.value.deliveryList.includes(item.deliveryName)
  );
  deliveryArray.forEach((item : any) => {
    //去重以及空数据判定
    if((!formData.value.transitDepotFromData.includes(item.transitDepotInfo.transitDepotName))) {
      if(item.transitDepotInfo.transitDepotName === '') {
        formData.value.transitDepotFromData.push('无')
      } else {
        formData.value.transitDepotFromData.push(item.transitDepotInfo.transitDepotName)
      }
    }
    
  })
    formData.value.carSum = deliveryArray.reduce((accumulator : number, currentItem : any) => {
    return accumulator + currentItem.carNumber; // 累加当前值
    }, 0); // 初始值为 0

    formData.value.routeSum = deliveryArray.reduce((accumulator : number, currentItem : any) => {
    return accumulator + currentItem.routeNumber; // 累加当前值
  }, 0); // 初始值为 0
  })
}

//获取表格信息
async function getTeamAddDataTransfROM() {
  if(formData.value.deliveryList.includes("无") || formData.value.deliveryList.length < 1) {
      formData.value.carSum = 0
      formData.value.routeSum = 0
      return Promise.resolve({
          deliveryList: [],
          carSum: formData.value.carSum,
          routeSum: formData.value.routeSum,
          teamName: props.teamName,
        })
    }
  const deliveryArray = 
    teamInfo.value.deliveryList.filter((item : any) => 
    formData.value.deliveryList.includes(item.deliveryName)
  );

  return Promise.resolve({
    deliveryList: deliveryArray,
    carSum: formData.value.carSum,
    routeSum: formData.value.routeSum,
    teamName: props.teamName,
  })
}

</script>

<style lang="less" scoped>
.addArea {
  color: black;
}
.areaForm {
  margin-left: 100px;

  .areaItem {
    font-size: 20px;
  }
}
.btns {
  display: flex;
  justify-content: center;
  color: black;
}

.transform {
  .content {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    font-size: 20px;
  }
}

</style>
