import { defineStore } from "pinia";
import type {
    IAreaData,
    IAreaGetData,
    IAreaAddData,
    IAreaDeleteData,
    IAreaUpdateData
} from '@/types/managerment';

import { queryArea,getAreaList,getCode,addAreaList,deleteAreaList,updateAreaList, getAddAreaInfo,getSearchAreaList } from "@/service/modules/management/area";

export const useAreaStore = defineStore('area', () => {
    
    //班组信息数据
    const areaList = ref<IAreaData[]>([])
    const areaTotalList = ref<IAreaData[]>([])
    const addTeamInfo = ref<any>()
    const searchTeamInfo = ref<any>()

    //查询班组变更信息
    async function queryAreaAddData(data: IAreaAddData) {
        const res :any = await queryArea(data)
        return res.data;
    }

    // 获取当前页班组信息
    async function getAreaData(params: IAreaGetData) {
        const res :any = await getAreaList(params)
        areaList.value = res.data.records
    }

    //获取全部班组信息
    async function getAreaTotalData(pageSize: number) {
        let pageNum = 1;
        let hasMoreData = true;
        areaTotalList.value = [];
        while (hasMoreData) {
            try {
                const res :any= await getAreaList({pageNum, pageSize})
                areaTotalList.value = [...areaTotalList.value,...res.data.records]
            // 检查是否还有更多数据
                hasMoreData = res.data.records.length === pageSize;
                pageNum++;
            } catch (error) {
                console.error('请求数据时出错:', error);
                hasMoreData = false; // 出现错误时停止请求
            }
        }
    }

    // 添加班组信息
    async function addAreaData(data: IAreaAddData) {
        const res = await addAreaList(data)
        return res
    }

    // 添加班组固定信息
    async function getAddTeamInfo() {
        const res = await getAddAreaInfo()
        addTeamInfo.value = res.data
    }

    // 删除班组信息
    async function deleteAreaData(params:IAreaDeleteData) {
        const res = await deleteAreaList(params)
        return res
    }

    // 更新班组信息
    async function updateAreaData(data:IAreaUpdateData) {
        const res = await updateAreaList(data)
        return res
    }

     // 搜索班组信息
     async function searchAreaData() {
        const res = await getSearchAreaList()
        searchTeamInfo.value = res.data
    }

    

    return {
        // 班组信息数据
        getAreaData,
        addAreaData,
        deleteAreaData,
        updateAreaData,
        getAddTeamInfo,
        getAreaTotalData,
        searchAreaData,
        queryAreaAddData,
        areaTotalList,
        areaList,
        addTeamInfo,
        searchTeamInfo
    }
})