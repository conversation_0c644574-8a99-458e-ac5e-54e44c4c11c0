<template>
  <el-dialog
    v-model="dialogVisible"
    :title="title"
    width="500px"
    :close-on-click-modal="false"
    :show-close="true"
    class="rename-dialog"
    @close="handleClose"
  >
    <div class="dialog-content">
      <div class="form-item">
        <div class="label"><span class="required">*</span>版本名：</div>
        <el-input
          v-model="formData.versionName"
          placeholder="请输入版本名"
          class="input-field"
        />
      </div>

      <div class="form-item">
        <div class="label">备注：</div>
        <el-input
          v-model="formData.versionInfo"
          type="textarea"
          :rows="5"
          maxlength="200"
          show-word-limit
          placeholder="请输入备注"
          class="input-field"
        />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleSave" class="save-btn">{{
          confirmButtonName
        }}</el-button>
        <el-button @click="closeDialog" class="cancel-btn">取消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
  import { ref, reactive } from "vue";
  import { ElMessage, ElDialog, ElButton, ElInput } from "element-plus";

  const title = ref("重命名为:");

  interface FormData {
    versionId: number;
    versionDb: string;
    versionName: string;
    versionInfo: string;
  }

  interface Emits {
    (e: "save", data: FormData): void;
  }

  const emit = defineEmits<Emits>();

  // 表单数据
  const formData = reactive<FormData>({
    versionId: 0,
    versionDb: "",
    versionName: "",
    versionInfo: "",
  });

  const confirmButtonName = ref<string>("保存");

  // 对话框可见性
  const dialogVisible = ref(false);

  // 打开对话框
  const openDialog = (
    initialData?: Partial<FormData>,
    save: boolean = false
  ) => {
    if (!save) {
      title.value = "重命名为";
      formData.versionName = initialData.versionName || "";
      formData.versionInfo = initialData.versionInfo || "";
      formData.versionDb = initialData.versionDb || "";
      formData.versionId = initialData.versionId || 0;
      confirmButtonName.value = "确定";
    } else {
      title.value = "另存为新版本";
      formData.versionDb = initialData.versionDb || "";
      formData.versionId = initialData.versionId || 0;
      confirmButtonName.value = "保存";
    }
    dialogVisible.value = true;
  };

  // 关闭对话框
  const closeDialog = () => {
    dialogVisible.value = false;
  };

  // 重置表单
  const resetForm = () => {
    formData.versionName = "";
    formData.versionInfo = "";
    formData.versionDb = "";
    formData.versionId = 0;
  };

  // 关闭对话框时重置表单
  const handleClose = () => {
    resetForm();
  };

  // 保存按钮点击事件
  const handleSave = () => {
    if (!formData.versionName && !formData.versionInfo) {
      ElMessage.warning("不能为空");
      return;
    }

    emit("save", { ...formData });
    closeDialog();
  };

  // 暴露方法和状态给父组件
  defineExpose({
    openDialog,
    closeDialog,
    resetForm,
    confirmButtonName,
    formData,
    dialogVisible,
  });
</script>
<style lang="less" scoped>
  .rename-dialog {
    :deep(.el-dialog) {
      background-color: #0a1929;
      border: 1px solid #1e3a5f;
      border-radius: 4px;

      .el-dialog__header {
        margin: 0;
        padding: 15px 20px;
        color: white;
        font-size: 16px;
        border-bottom: 1px solid #1e3a5f;

        .el-dialog__title {
          color: white;
        }

        .el-dialog__close {
          color: #a0a0a0;
          font-size: 20px;

          &:hover {
            color: white;
          }
        }
      }

      .el-dialog__body {
        padding: 20px;
        color: white;
      }

      .el-dialog__footer {
        padding: 10px 20px 20px;
        border-top: none;
      }
    }

    .dialog-content {
      .form-item {
        margin-bottom: 15px;

        .label {
          margin-bottom: 8px;
          font-size: 14px;

          .required {
            color: #f56c6c;
            margin-right: 4px;
          }
        }

        .input-field {
          width: 100%;

          :deep(.el-input__wrapper) {
            background-color: transparent;
            border: 1px solid #1e3a5f;
            box-shadow: none;

            .el-input__inner {
              color: white;

              &::placeholder {
                color: #8c8c8c;
              }
            }
          }

          :deep(.el-textarea__inner) {
            background-color: transparent;
            border: 1px solid #1e3a5f;
            color: white;

            &::placeholder {
              color: #8c8c8c;
            }
          }
        }
      }
    }

    .dialog-footer {
      display: flex;
      justify-content: center;
      gap: 20px;

      .save-btn,
      .cancel-btn {
        width: 120px;
        height: 40px;
        border-radius: 4px;
      }

      .save-btn {
        background-color: #a4cddf;
        border-color: #a4cddf;
        color: #0a1929;

        &:hover {
          background-color: #8bbfd5;
          border-color: #8bbfd5;
        }
      }

      .cancel-btn {
        background-color: #a4cddf;
        border-color: #a4cddf;
        color: #0a1929;

        &:hover {
          background-color: #8bbfd5;
          border-color: #8bbfd5;
        }
      }
    }
  }
</style>
