<template>
    <div class="addActualDialog">
      <el-dialog v-model="addActualOpen" title="添加实情信息" width="60%" height="70%" :close-on-click-modal="false" @close="closeAdd">
        <div class="flex-center">
          <el-form label-width="auto" class="areaForm" :model="totalFields" ref="formRef" :rules="rules">
          <div class="flex">
            <el-form-item label="车牌号" prop="licensePlateNumber">
              <el-select
              style="width: 250px;"
              placeholder="请选择"
              v-model="totalFields.licensePlateNumber"
            >
            <el-option :label="item" :value="item" v-for="(item) in store.actualInfo.licensePlateNumberList" :key="item +'9'"/>
            </el-select>
          </el-form-item>
          <el-form-item label="配送域" prop="deliveryAreaName">
            <el-select
              style="width: 250px;"
              placeholder="请选择"
              v-model="totalFields.deliveryAreaName"
            >
            <el-option :label="item" :value="item" v-for="(item) in store.actualInfo.deliveryNameList" :key="item +'9'"/>
            </el-select>
          </el-form-item>
          </div>
          <div class="flex">
            <el-form-item label="驾驶人" prop="carDriverName">
            <el-select
              style="width: 250px;"
              placeholder="请选择"
              v-model="totalFields.carDriverName"
            >
            <el-option :label="item" :value="item" v-for="(item) in store.actualInfo.carDriverList" :key="item +'9'"/>
            </el-select>
          </el-form-item>
          <el-form-item label="星期" prop="week">
            {{ totalFields.week }}
          </el-form-item>
          </div>
          <div class="flex">
            <el-form-item label="实际载货量" prop="actualLoad" :style="{display: 'flex'}">
              <el-input
                style="width: 200px;"
                placeholder="点击输入"
                v-model="totalFields.actualLoad"
              >
              </el-input>
              <div class="tons">吨</div>
            </el-form-item>
            <el-form-item label="日期" prop="date">
              <el-date-picker
                v-model="totalFields.date"
                type="date"
                placeholder="选择日期"
                style="width: 200px"
                format="YYYY/MM/DD"
                value-format="YYYY/MM/DD"
                @change="getWeekday(totalFields.date)"
                @clear="totalFields.week = ''"
              />
            </el-form-item>
          </div>
          <div class="flex">
            <el-form-item label="实际工作时长" prop="actualTime" :style="{display: 'flex'}">
              <el-input
                style="width: 200px;"
                placeholder="点击输入"
                v-model="totalFields.actualTime"
              >
              </el-input>
              <div class="tons">小时</div>
            </el-form-item>
            <el-form-item label="路线" prop="route">
                <el-input
                style="width: 200px;"
                placeholder="点击输入"
                v-model="totalFields.route"
                >
                </el-input>
            </el-form-item>
          </div>
        </el-form>
        </div>
  
        <div class="btns">
          <el-button type="primary" @click="closeAdd">取消</el-button>
          <el-button type="primary" style="margin-left: 100px" @click="confirmAdd"
            >确定</el-button
          >
        </div>
      </el-dialog>
    </div>
</template>
  
<script setup lang="ts">
  import { carStore } from "@/store/managerment/car";
  const store = carStore()
  const addActualOpen = ref(false);
  defineExpose({
    addActualOpen
  });
  
  const formRef = ref<any>()
  const addEmit = defineEmits(["confirmAdd"]);
  const totalFields = ref<any>({
    licensePlateNumber: '',
    deliveryAreaName: '',
    carDriverName: '',
    week: '',
    actualLoad: '',
    date: '',
    actualTime: '',
    route: ''
  })
  const rules = reactive({
  licensePlateNumber: [
    { required: true, message: '请输入', trigger: 'blur' },
  ],
  carDriverName: [
    {
      required: true,
      message: '请选择',
      trigger: 'change',
    },
  ],
  date: [
    {
      required: true,
      message: '选择日期',
      trigger: 'change',
    },
  ]
  })


  //确认添加
  async function confirmAdd() {
    if (!formRef.value) return
    await formRef.value.validate((valid : any) => {
    if (valid) {
      addEmit("confirmAdd", totalFields.value)
      formRef.value.resetFields()
      addActualOpen.value = false
    } else {
      ElMessage({
      message: '添加失败',
      type: 'warning'
      })
    }
  })
  }
  
  //取消添加
  const closeAdd = () => {
    formRef.value.resetFields()
    addActualOpen.value = false;
  };

  function getWeekday(dateString : string) {
    const date = new Date(dateString);
    const weekdays = ["星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"];
    totalFields.value.week = weekdays[date.getDay()]
  }
</script>
  
<style lang="less" scoped>
.flex-center {
  width: 100%;
  display: flex;
  justify-content: center;
}
.flex {
  display: flex;
}
  .areaForm {
    .areaItem {
      font-size: 20px;
    }
  }
  .btns {
    display: flex;
    justify-content: center;
    color: black;
  }
  
  .transform {
    .content {
      display: flex;
      justify-content: center;
      flex-wrap: wrap;
      font-size: 20px;
    }
  }
  
  .tons {
    font-size: 20px;
    margin-left: 20px;
  }
</style>
  