<template>
  <div class="Computer">
    <div class="btn-content" v-if="!route.meta.isShow">
      <el-badge class="item">
        <el-button
          :class="{ vertical: true, active: currentRoute === 'route' }"
          @click="routerChange('route')"
          >路径计算</el-button
        >
      </el-badge>
      <el-badge class="item">
        <el-button
          :class="{ vertical: true, active: currentRoute === 'area' }"
          @click="routerChange('area')"
          >聚集区计算</el-button
        >
      </el-badge>
      <el-badge class="item">
        <el-button
          type="primary"
          :class="{
            vertical: true,
            active:
              currentRoute === 'pos' ||
              currentRoute === 'pickup' ||
              currentRoute === 'MilkRun',
          }"
          @click="routerChange('MilkRun')"
          >定点取货</el-button
        >
      </el-badge>
    </div>
    <div class="content">
      <RouterView></RouterView>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { useRouter, useRoute } from "vue-router";
  const router = useRouter();
  const route = useRoute();
  const currentRoute = ref(route.name);
  onMounted(() => {
    if (route.name == "AreaAdjust") {
      currentRoute.value = "area";
    }
  });
  function routerChange(route: any) {
    router.push(`/home/<USER>/${route}`);
    currentRoute.value = route;
  }
</script>
<style lang="scss" scoped>
  .Computer {
    margin-top: 2vh;
    display: flex;

    .btn-content {
      width: 20px;
      display: flex;
      flex-direction: column;

      .el-button {
        --el-button-bg-color: #15335f;
        border: 0;
        color: #fff;
        --el-button-text-color: #fff;
        height: 16vh !important;
        width: 2.2vw;
        font-size: 1.2vw !important;
        border-radius: 0;
      }

      .el-button:hover {
        color: #fff;
        background: #84aeeb;
      }

      .el-button.active {
        color: #fff;
        border-color: #73e1ff;
        background-color: #84aeeb;
        outline: none;
      }
    }

    .content {
      margin-left: 1.5vw;
      flex: 1;
    }
  }
</style>
