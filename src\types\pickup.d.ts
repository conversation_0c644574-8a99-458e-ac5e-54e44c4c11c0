export interface IPickupUserGetListData {
  color?: number;
  customCode?: string;
  deliveryDistance?: number;
  gear?: string;
  pageNum: number;
  pageSize: number;
  pickupContainers?: string;
  storeAddress?: string;
  storeName?: string;
  type?: string;
}

export interface IPickupUserCalculateData {
  avgDistance: number;
  gear: number;
  levelParam: number;
  roadGrade: number;
}

export interface IPickupUserUpdateData {
  deliveryDistance: number;
  id: number;
  locks: number;
  pickupContainers: string;
  type: string;
}

//导入日志
export interface IPickupLogImport {
  pageNum: number;
  pageSize: number;
  type: string;
}

//下载表格
export interface IFromDownload {
  code: number;
}

//下载日志
export interface ILogDownload {
  fileName: string;
  importTime: string;
}

//删除日志
export interface IDeleteLog {
  logsId: number;
}

//待分配
export interface IUnassignedList {
  ids: number[];
}

//取货户数据
export interface IPickupUserData {
  id: number;
  customerCode: string;
  contactName: string;
  customerManagerName: string;
  contactPhone: string;
  storeAddress: string;
  roadGrade: string;
  gear: string;
  deliveryDistance: number;
  pickupContainers: string;
  type: string;
  weights: number;
  locks: number;
  color: number;
  longitude: number;
  latitude: number;
  accumulationName: string;
  routeName?: string;
}
