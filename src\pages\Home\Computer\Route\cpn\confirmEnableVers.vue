<template>
  <el-dialog
    v-model="dialogVisible"
    title="确定启用该版本?"
    width="400px"
    :close-on-click-modal="false"
    :show-close="true"
    class="confirm-dialog"
    @close="handleClose"
  >
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleConfirm" class="confirm-btn">确定</el-button>
        <el-button @click="closeDialog" class="cancel-btn">取消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
  import { ref } from "vue";
  import { ElDialog, ElButton } from "element-plus";

  interface Props {
    versionId: string;
  }

  interface Emits {
    (e: "confirm", id: string): void;
    (e: "cancel"): void;
  }

  const props = withDefaults(defineProps<Props>(), {
    versionId: "",
  });

  const emit = defineEmits<Emits>();

  // 对话框可见性
  const dialogVisible = ref(false);

  // 打开对话框
  const openDialog = () => {
    console.log(props.versionId);
    dialogVisible.value = true;
  };

  // 关闭对话框
  const closeDialog = () => {
    dialogVisible.value = false;
    emit("cancel");
  };

  // 处理关闭事件
  const handleClose = () => {
    closeDialog();
  };

  // 确认按钮点击事件
  const handleConfirm = () => {
    emit("confirm", props.versionId);
    closeDialog();
  };

  // 暴露方法给父组件
  defineExpose({
    openDialog,
    closeDialog,
    dialogVisible,
  });
</script>

<style lang="less" scoped>
  .confirm-dialog {
    :deep(.el-dialog) {
      background-color: #0a1929;
      border: 1px solid #1e3a5f;
      border-radius: 4px;
      max-width: 90%;
      margin: 0 auto;
      display: flex;
      flex-direction: column;
      align-items: center;

      .el-dialog__header {
        margin: 0;
        padding: 15px 20px;
        color: white;
        font-size: 16px;
        border-bottom: 1px solid #1e3a5f;
        width: 100%;
        text-align: center;

        .el-dialog__title {
          color: white;
          font-weight: normal;
        }

        .el-dialog__close {
          color: #a0a0a0;
          font-size: 20px;

          &:hover {
            color: white;
          }
        }
      }

      .el-dialog__body {
        padding: 20px;
        color: white;
        width: 100%;
        text-align: center;
      }

      .el-dialog__footer {
        padding: 10px 20px 20px;
        border-top: none;
        width: 100%;
      }
    }

    .dialog-footer {
      display: flex;
      justify-content: center;
      gap: 20px;

      .confirm-btn,
      .cancel-btn {
        width: 120px;
        height: 40px;
        border-radius: 4px;
      }

      .confirm-btn {
        background-color: #a4cddf;
        border-color: #a4cddf;
        color: #0a1929;

        &:hover {
          background-color: #8bbfd5;
          border-color: #8bbfd5;
        }
      }

      .cancel-btn {
        background-color: #a4cddf;
        border-color: #a4cddf;
        color: #0a1929;

        &:hover {
          background-color: #8bbfd5;
          border-color: #8bbfd5;
        }
      }
    }
  }
</style>
