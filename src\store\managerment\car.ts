import { defineStore } from "pinia";
import type {
  ICarAddData,
  ICarDeleteData,
  ICarUpdateData,
  ICarData,
  IAcutualAddCar,
  IAcutualDeleteCar,
  IAcutualUpdateCar,
  ILogDelete,
  ILogDownload,
  ILogImport,
  IFromDownload,
  ICarGetData,
} from "@/types/car";

import {
  addCar,
  deleteCar,
  updateCar,
  getCar,
  searchCarInfo,
  addCarInfo,
  updateActualCar,
  getActualCarDownBox,
  getActualCar,
  deleteActualCar,
  addActualCar,
  importActual,
  importLog,
  deleteLog,
  downloadLog,
  downloadNullFrom,
} from "@/service/modules/management/car";

export const carStore = defineStore("car", () => {
  const carTotalList = ref<ICarData[]>([]);
  const carSearchInfo = ref<any>();
  const carAddInfo = ref<any>();
  const actualInfo = ref<any>();
  const actualCarTotalList = ref<any>([]);
  const noteAllList = ref<any>();
  const noteList = ref<any>();
  const noteData = ref<any>();
  const isInTable = ref<any>(false);

  async function addCarData(params: ICarAddData) {
    const res = await addCar(params);
    return res;
  }

  async function deleteCarData(params: ICarDeleteData) {
    const res = await deleteCar(params);
    return res;
  }

  async function updateCarData(params: ICarUpdateData) {
    const res = await updateCar(params);
    return res;
  }

  async function getAllCarData(pageSize: number) {
    let pageNum = 1;
    let hasMoreData = true;
    carTotalList.value = [];
    while (hasMoreData) {
      try {
        const res: any = await getCar({ pageNum, pageSize });
        carTotalList.value = [...carTotalList.value, ...res.data.records];
        // 检查是否还有更多数据
        hasMoreData = res.data.records.length === pageSize;
        pageNum++;
      } catch (error) {
        console.error("请求数据时出错:", error);
        hasMoreData = false; // 出现错误时停止请求
      }
    }
  }

  async function getCarData(params: ICarGetData) {
    const res: any = await getCar(params);
    return res;
  }

  //搜索车辆下拉框
  async function searchCarInfoData() {
    const res: any = await searchCarInfo();
    carSearchInfo.value = res.data;
  }

  //添加车辆下拉框
  async function addCarInfoData() {
    const res: any = await addCarInfo();
    carAddInfo.value = res.data;
  }

  //添加实情
  async function addCarActual(params: IAcutualAddCar) {
    const res: any = await addActualCar(params);
    return res;
  }

  //删除实情
  async function deleteCarActual(params: IAcutualDeleteCar) {
    const res = await deleteActualCar(params);
    return res;
  }

  //修改实情
  async function updateCarActual(params: IAcutualUpdateCar) {
    const res = await updateActualCar(params);
    return res;
  }

  //获得全部实情
  async function getTotalCarActual(pageSize: number) {
    let pageNum = 1;
    let hasMoreData = true;
    actualCarTotalList.value = [];
    while (hasMoreData) {
      try {
        const res: any = await getActualCar({ pageNum, pageSize });
        actualCarTotalList.value = [
          ...actualCarTotalList.value,
          ...res.data.records,
        ];
        hasMoreData = res.data.records.length === pageSize;
        pageNum++;
      } catch (error) {
        console.error("请求数据时出错:", error);
        hasMoreData = false; // 出现错误时停止请求
      }
    }
  }

  //获得实情下拉框
  async function getActualDownBox() {
    const res: any = await getActualCarDownBox();
    actualInfo.value = res.data;
  }

  //导入实情
  async function importCarActual(data: FormData, config: any) {
    const res = await importActual(data, config);
    return res;
  }

  //导入日志
  async function importCarLog(params: ILogImport) {
    const res = await importLog(params);
    noteData.value = res.data;
  }

  //导入全部日志
  async function importAllLog(pageSize: number) {
    let pageNum = 1;
    const type = "1";
    let hasMoreData = true;
    noteAllList.value = [];
    while (hasMoreData) {
      try {
        const res: any = await importLog({ pageNum, pageSize, type });
        noteAllList.value = [...noteAllList.value, ...res.data.records];
        // 检查是否还有更多数据
        hasMoreData = res.data.records.length === pageSize;
        pageNum++;
      } catch (error) {
        console.error("请求数据时出错:", error);
        hasMoreData = false; // 出现错误时停止请求
      }
    }
  }

  //删除日志
  async function deleteCarLog(params: ILogDelete) {
    const res = await deleteLog(params);
    return res;
  }

  //下载日志
  async function downloadCarLog(params: ILogDownload) {
    const res: any = await downloadLog(params);
    return res;
  }

  async function downloadFrom(params: IFromDownload) {
    const res: any = await downloadNullFrom(params);
    return res;
  }

  function updatelogList(pageNum: number, pageSize: number) {
    noteList.value = noteAllList.value.slice(
      pageNum * pageSize - pageSize,
      pageNum * pageSize
    );
  }

  function updateIsinTable() {
    isInTable.value = !isInTable.value;
  }

  return {
    addCarData,
    deleteCarData,
    updateCarData,
    getAllCarData,
    getCarData,
    searchCarInfoData,
    addCarInfoData,
    addCarActual,
    deleteCarActual,
    updateCarActual,
    getTotalCarActual,
    getActualDownBox,
    importCarActual,
    deleteCarLog,
    downloadCarLog,
    importCarLog,
    importAllLog,
    updatelogList,
    downloadFrom,
    updateIsinTable,
    isInTable,
    carSearchInfo,
    carTotalList,
    carAddInfo,
    actualCarTotalList,
    actualInfo,
    noteAllList,
    noteList,
    noteData,
  };
});
