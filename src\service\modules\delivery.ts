import request from "../index";

import type { IDeliveryGetList,IDelivery<PERSON>ddList,IDeliveryDeleteList,IDeliveryUpdateList } from "@/types/delivery";
import { IRequest } from "../request/type";

// 获取配送域信息数据
export function getDeliveryList(params: IDeliveryGetList) {
    return request.get<IRequest<IDeliveryGetList>>({
        url: 'datamanagement/deliveryList',
        params,
    })
}

// 获取搜索列表数据
export function getDeliverySelectListData() {
    return request.post<IRequest<any>>({
        url: 'datamanagement/getSelect',
    })
}

// 获取添加固定信息
export function getDeliveryAddInfo() {
    return request.post<IRequest<any>>({
        url: 'datamanagement/addDeliveryDownBox',
    })
}

// 修改配送域
export function updateDeliveryData(params: IDeliveryUpdateList) {
    return request.post<IRequest<IDeliveryUpdateList>>({
        url: 'datamanagement/updateDelivery',
        params
    })
}

// 删除配送域
export function deleteDeliveryData(params: IDeliveryDeleteList) {
    return request.delete<IRequest<IDeliveryDeleteList>>({
        url: 'datamanagement/deleteDelivery',
        params
    })
}

// 添加配送域
export function addDeliveryData(params: IDeliveryAddList) {
    return request.post<IRequest<IDeliveryAddList>>({
        url: 'datamanagement/addDelivery',
        params
    })
}