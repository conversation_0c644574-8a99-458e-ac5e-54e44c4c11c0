export interface IAreaData {
    carSum: number,
    deliveryAreaName: string,
    routeSum: number,
    teamId: number,
    teamName: string,
    transitDepotName: string
}


export interface IAreaGetData {
    /**
     * 获取班组数据
     */
    pageNum: number;
    pageSize: number;
    teamName?: string;
    transitDepotName?: string;
}

export interface IAreaDeleteData {
    /**
     * 删除班组数据
     */
    teamId: number;
}

export interface IAreaAddData {
    /**
     * 添加班组数据
     */
    carSum: number;
    deliveryList: any;
    routeSum: number;
    teamName: string;
}

export interface IAreaUpdateData {
    /**
     * 更新班组数据
     */
    carSum: number;
    deliveryList: any;
    routeSum: number;
    teamId: number;
    teamName: string;
}
