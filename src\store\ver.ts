import { defineStore } from "pinia";
import {
  delVer,
  getRoute,
  getVerList,
  changeSource,
  changeVer,
  updateVer,
  getCurrentVer,
  saveNewVersion,
} from "@/service/modules/ver";

export const useVersStore = defineStore("ver", () => {
  //获取列表
  async function getVerListData() {
    const res = await getVerList();
    return res;
  }

  //删除
  async function delVerData(id: any) {
    const res = await delVer(id);
    return res;
  }

  //切换
  async function changeVerData(version: any) {
    const res = await changeVer(version);
    return res;
  }

  //获取路线
  async function getRouteData(params: any) {
    const res = await getRoute(params);
    return res;
  }

  //切换数据源
  async function changeSourceData(dbName: any) {
    const res = await changeSource(dbName);
    return res;
  }

  //版本信息更新
  async function updateVerData(params: any) {
    const res = await updateVer(params);
    return res;
  }
  //当前版本
  async function getCurrentVerData() {
    const res = await getCurrentVer();
    return res;
  }

  //版本另存
  async function saveNewVersionData(params: any) {
    const res = await saveNewVersion(params);
    return res;
  }

  return {
    getVerListData,
    delVerData,
    changeVerData,
    getRouteData,
    changeSourceData,
    updateVerData,
    getCurrentVerData,
    saveNewVersionData,
  };
});
