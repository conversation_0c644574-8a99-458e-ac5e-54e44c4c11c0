<template>
  <div class="map-container">
    <div ref="mapContainer" class="map-box"></div>
    <div class="legend-box">
      <div v-for="(icon, type) in titleMap" :key="type" class="legend-item">
        <img :src="icon" style="width: 22px; height: 22px" />
        <span style="color: black">{{ type }}</span>
      </div>
    </div>
    <!-- 添加加载遮罩层 -->
    <div v-if="loading" class="map-loading-mask">
      <div class="loading-spinner"></div>
      <div class="loading-text">地图加载中...</div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { MAP_KEY, SECURITY_CODE } from "@/utils/getMapKey";
  import { modifyUserAgent } from "@/utils/modifyUserAgent";
  import AMapLoader from "@amap/amap-jsapi-loader";
  window._AMapSecurityConfig = {
    securityJsCode: SECURITY_CODE,
  };
  import { ref, onMounted, onUnmounted } from "vue";

  modifyUserAgent();
  const mapContainer = ref<HTMLElement>();
  let map: any = null;
  defineExpose({
    getMap,
  });
  let markers: any[] = [];
  let infoWindow: any = null;
  let nowDrive: any[] = [];

  // 类型定义
  type TitleType = "取货地" | "商户";
  type PointType = "A" | "B" | "C" | "D" | "E" | "F";
  interface Info {
    name: string;
    address?: string;
    distance?: number;
    stores?: string;
    dian?: number[];
    posList?: number[][];
  }
  interface MapPoint {
    lnglat: [number, number];
    type: PointType;
    info: Info;
  }
  // 组件配置
  const props = defineProps({
    list: {
      type: Array as PropType<MapPoint[]>,
      required: true,
      validator: (val: any) => Array.isArray(val),
    },
    mapType: {
      type: Number as PropType<0 | 1>,
      default: 1,
    },
    // 添加loading属性，由外部控制
    loading: {
      type: Boolean,
      default: false,
    },
  });

  const selectedMarker = ref<any>(null);

  // 核心渲染逻辑
  const updateMarkers = () => {
    // 清理旧标记
    markers.forEach((marker) => {
      map.remove(marker);
      marker.off("mouseover  click mouseout");
    });
    markers = [];

    // 渲染新标记
    props.list.forEach((point) => {
      if (point.type === "A" || typeof point.type === "undefined") {
        return;
      }
      const icon = new AMap.Icon({
        image: iconMap[point.type],
        size: new AMap.Size(12, 12),
        imageSize: new AMap.Size(12, 12),
      });

      const marker: any = new AMap.Marker({
        position: point.lnglat,
        icon,
        map: map,
        extData: iconMap[point.type],
      });

      // 悬浮交互事件
      marker.on("mouseover", (e: any) => {
        if (point.type === "E" || typeof point.type === "undefined") {
          infoWindow.setContent(`
      <div class="info-card">
        <h3>${point.info.name}</h3>
      </div>
      `);
          infoWindow.open(map, e.target.getPosition());
          return;
        }
        infoWindow.setContent(`
      <div class="info-card">
        <h3>${point.info.name}</h3>
        ${
          point.info.address
            ? `<p>最佳取货地：${point.info.address}</p>
        <p>距离：${point.info.distance}公里</p>`
            : `<p>关联商户: ${point.info.stores} </p>`
        }

      </div>
      `);
        infoWindow.open(map, e.target.getPosition());
      });
      marker.on("click", (e: any) => {
        if (point.type === "E" || typeof point.type === "undefined") {
          return;
        }
        let icon = new AMap.Icon({
          image: iconMap[point.type],
          size: new AMap.Size(12, 12),
          imageSize: new AMap.Size(12, 12),
        });
        if (selectedMarker.value) {
          icon = new AMap.Icon({
            image: selectedMarker.value.getExtData(),
            size: new AMap.Size(12, 12),
            imageSize: new AMap.Size(12, 12),
          });

          selectedMarker.value.setIcon(icon);
          nowDrive.forEach((item: any) => {
            if (item) {
              item.clear();
            }
          });
          nowDrive = [];
        }
        const newicon = new AMap.Icon({
          image: iconMap[point.type].replace("-1", "-2"),
          size: new AMap.Size(12, 12),
          imageSize: new AMap.Size(12, 12),
        });
        selectedMarker.value = e.target;
        e.target.setIcon(newicon);
        if (point.info && point.info.dian) {
          const driving = new AMap.Driving({
            policy: 2,  // 距离最短策略，与后端strategy=2保持一致

            map: map,
            hideMarkers: true,
          });
          const pointA = [point.lnglat[0], point.lnglat[1]];
          const pointB = point.info.dian[0];
          //搜索完成后，将自动绘制路线到地图上
          driving.search(pointA, pointB);
          nowDrive.push(driving);
        }
        if (point.info.posList && point.info.posList?.length > 0) {
          const pointA = [point.lnglat[0], point.lnglat[1]];
          point.info.posList.forEach((item) => {
            const driving = new AMap.Driving({
              policy: 2,  // 距离最短策略，与后端strategy=2保持一致
              map: map,
              hideMarkers: true,
            });
            const pointB = item;
            //搜索完成后，将自动绘制路线到地图上
            driving.search(pointA, pointB);
            nowDrive.push(driving);
          });
        }
      });

      marker.on("mouseout", () => infoWindow.close());
      markers.push(marker);
    });
  };

  // 监听list变化，更新标记
  watch(
    () => props.list,
    () => {
      if (map) {
        updateMarkers();
      }
    },
    { deep: true }
  );

  const iconMap: Record<PointType, string> = {
    A: "/1-1.png",
    B: "/2-1.png",
    C: "/3-1.png",
    D: "/4-1.png",
    E: "/jinyong.png",
    F: "/8-1.png",
  };

  const titleMap: Record<TitleType, string> = {
    取货地: "/6-1.png",
    商户: "/5-1.png",
  };

  onMounted(() => {
    AMapLoader.load({
      key: MAP_KEY,
      version: "2.0",
      plugins: ["AMap.DistrictSearch", "AMap.MarkerCluster", "AMap.Driving"],
    })
      .then(async (AMap: any) => {
        const district = new AMap.DistrictSearch({
          subdistrict: 1,
          extensions: "all",
          level: "province",
        });
        // 行政区域边界限定
        district.search("韶关市", (status: string, result: any) => {
          const bounds = result.districtList[0].boundaries;
          const mask = [];
          for (let i = 0; i < bounds.length; i++) {
            mask.push([bounds[i]]);
          }

          // 核心地图初始化
          map = new AMap.Map(mapContainer.value, {
            mask: mask, // 为Map实例制定掩模的路径,各图层将值显示路径范围内图像,3D模式下有效
            viewMode: "3D",
            zoom: 9,
            center: [113.58, 24.81],
            expandZoomRange: true, // 开启显示范围设置
            zooms: [9, 18], //最小显示级别为7，最大显示级别为20
            zoomEnable: true, // 是否可以缩放地图
            resizeEnable: true,
          });

          const borderLayer = new AMap.Polygon({
            path: bounds, // 使用原始边界坐标
            strokeColor: "#788fbe", // 浅蓝色
            strokeWeight: 6, // 6像素粗边
            fillOpacity: 0.1, // 透明度
            strokeStyle: "solid",
          });
          map.add(borderLayer);
          // 监听地图缩放事件，动态调整虚线密度
          // 添加点击事件演示

          // 信息窗初始化
          infoWindow = new AMap.InfoWindow({
            isCustom: true,
            closeWhenClickMap: true,
            offset: new AMap.Pixel(15, -40),
          });

          updateMarkers();
        });
      })
      .catch((e: any) => {
        ElMessage.error("地图加载失败");
        console.error(" 地图加载异常:", e);
      });
  });

  function getMap() {
    return map;
  }

  onUnmounted(() => {
    if (map) {
      map.destroy();
      map = null;
    }
  });
</script>

<style scoped>
  /* 添加加载遮罩层样式 */
  .map-loading-mask {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.7);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 2000;
  }

  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 10px;
  }

  .loading-text {
    color: #333;
    font-size: 16px;
    font-weight: bold;
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  :deep(.info-card) {
    padding: 12px;
    min-width: 180px;
    background: white;
    border-radius: 6px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);

    h3 {
      margin: 0 0 8px;
      color: #1a73e8;
      font-size: 15px;
    }

    p {
      margin: 4px 0;
      color: #666;
      font-size: 13px;
    }
  }

  .map-container {
    position: relative;
    width: 100%;
    height: 100%;
    min-height: 400px;
  }

  .map-box {
    height: 100%;
    width: 100%;
  }

  .legend-box {
    position: absolute;
    left: 20px;
    top: 20px;
    opacity: 0.8;
    background: rgba(255, 255, 255, 0.95);
    padding: 12px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    z-index: 1000;
  }

  .legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 6px 0;
  }

  :deep(.info-card) {
    padding: 12px;
    min-width: 180px;
    background: white;
    border-radius: 6px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);

    h3 {
      margin: 0 0 8px;
      color: #1a73e8;
      font-size: 15px;
    }

    p {
      margin: 4px 0;
      color: #666;
      font-size: 13px;
    }
  }
</style>
