<template>
    <el-dialog
      v-model="vis"
      title="提醒"
      class="transform"
      width="30%"
      align-center
      :close-on-click-modal="false"
    >
      <div class="content">{{props.dType}}信息已变更</div>
      <p class="content">请到路径计算页面重新计算</p>
      <div class="content" style="color: red;cursor: pointer;" @click="openDirection">如何重新计算?</div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="confirmDialog">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
    <pathDirection ref="pathDiRef"/>
  </template>
  
<script setup lang="ts">
import pathDirection from './pathDirection.vue';
const vis = ref<boolean>(false)
const pathDiRef = ref<any>()
const props = defineProps(['dType'])

defineExpose({
  vis
});

function confirmDialog() {
  vis.value = false
}
  
function openDirection() {
  vis.value = false
  pathDiRef.value.vis = true
}  
</script>
  
<style lang="less" scoped>
.transform {
    .content {
      display: flex;
      justify-content: center;
      font-size: 20px;
    }
}
</style>
  