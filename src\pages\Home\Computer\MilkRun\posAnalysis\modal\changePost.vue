<template>
  <div class="change">
    <el-dialog
      v-model="changePostOpen"
      width="60%"
      height="70%"
      :modal="false"
      title="修改选址"
    >
      <div class="flex-center">
        <el-form
          label-width="auto"
          width="100%"
          class="areaForm"
          :model="data"
          ref="formRef"
        >
          <el-form-item label="名称" prop="pickupName">
            <el-input placeholder="请输入" v-model="data.pickupName"></el-input>
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select
              placeholder="请选择"
              v-model="data.status"
              @change="openTip"
            >
              <el-option label="启用(已分配)" :value="3" />
              <el-option label="启用(未分配)" :value="2" />
              <el-option label="禁用" :value="1" />
            </el-select>
          </el-form-item>
          <el-form-item label="经度" prop="longitude">
            <el-input placeholder="请输入" v-model="data.longitude"></el-input>
          </el-form-item>
          <el-form-item label="纬度" prop="latitude">
            <el-input placeholder="请输入" v-model="data.latitude"></el-input>
          </el-form-item>
          <el-form-item label="取货地类型" prop="type">
            <el-select placeholder="请选择" v-model="data.type">
              <el-option label="邮局" value="邮局" />
              <el-option label="烟站" value="烟站" />
              <el-option label="村委会" value="村委会" />
              <el-option label="其他" value="其他" />
            </el-select>
          </el-form-item>
          <el-form-item label="详细地址" prop="pickupAddress">
            <el-input
              placeholder="请输入"
              v-model="data.pickupAddress"
            ></el-input>
          </el-form-item>
          <el-form-item label="关联商户" prop="storeIds">
            <div>
              <el-tag v-if="data.stores && data.stores.length <= 0">无</el-tag>
              <el-tag
                v-for="(item, index) in data.stores"
                closable
                :key="item.storeId"
                @close="handleClose(item.storeId)"
                >{{ item.storeName }}</el-tag
              >
            </div>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="btns">
          <el-button type="primary" @click="closeChange">取消</el-button>
          <el-button
            type="primary"
            style="margin-left: 100px"
            @click="confirmChange"
            >确定</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
  <update-tip ref="tip"></update-tip>
</template>

<script setup lang="ts">
  import updateTip from "./updateTip.vue";
  const changePostOpen = ref<boolean>(false);
  defineExpose({ changePostOpen });
  const change = defineEmits(["change"]);
  const deleteIds = ref<any[]>([]);
  const tip = ref<any>();

  const props = defineProps({
    row: {
      type: Object,
      default: {},
    },
  });
  const data = ref(JSON.parse(JSON.stringify(props.row)));

  watch(
    changePostOpen,
    (newVal) => {
      if (newVal) {
        deleteIds.value = [];
        data.value = JSON.parse(JSON.stringify(props.row));
      } else {
        data.value = {};
      }
    },
    { deep: true, immediate: true }
  ); // 立即执行初始化

  const formRef = ref<any>();
  const closeChange = () => {
    changePostOpen.value = false;
  };
  function handleClose(tagId: any) {
    data.value.stores = data.value.stores.filter(
      (item: any) => item.storeId !== tagId
    );
    deleteIds.value.push(tagId);
  }

  function openTip(e: any) {
    if (e === 3) {
      return;
    }
    tip.value.confirmDialogVis = true;
  }

  const confirmChange = () => {
    // const other = JSON.parse(JSON.stringify(data.value));
    data.value.storeIds = deleteIds.value;
    delete data.value.stores;
    console.log(data.value);
    console.log(props.row);
    change("change", data.value);
    changePostOpen.value = false;
  };
</script>

<style scoped lang="less">
  .flex-center {
    width: 100%;
    display: flex;
    justify-content: center;
  }
  .btns {
    display: flex;
    justify-content: center;
    color: black;
  }
  .areaForm {
    .areaItem {
      font-size: 20px;
    }
    :deep(.el-select) {
      width: 10vw;
      height: 3.6vh;
    }
    :deep(.el-input) {
      width: 10vw;
      height: 3.6vh;
    }
    p {
      font-size: 20px;
      width: 10vw;
      height: 6vh;
    }
    :deep(.el-form-item) {
      margin-right: 10px; // 调整表单项间距
      &:last-child {
        flex-basis: 100%;
      }
    }
    :deep(.el-form-item__content),
    :deep(.el-form-item__label) {
      width: 12vw; // 统一标签和内容宽度
    }
    display: flex;
    flex-wrap: wrap;
    justify-content: center; // 水平居中
    max-width: 80%; // 控制表单最大宽度
  }
</style>
