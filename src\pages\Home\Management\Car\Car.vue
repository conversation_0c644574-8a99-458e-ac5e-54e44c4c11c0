<template>
  <div class="Board">
    <div class="board-right">
      <BorderBox9 :color="['#90ade8', '#90ade8']">
        <router-view></router-view>
      </BorderBox9>
    </div>
    <div class="btn-content">
      <el-badge class="item" :hidden="!logisticsMount">
        <el-button
          type="primary"
          :class="{ vertical: true, active: currentIndex === 1 }"
          @click="routerChange('message')"
          >基本信息</el-button
        >
      </el-badge>
      <el-badge class="item" :hidden="!marketingMount">
        <el-button
          type="primary"
          :class="{ vertical: true, active: currentIndex === 2 }"
          @click="routerChange('work')"
          >工作实情</el-button
        >
      </el-badge>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { ref } from "vue";
  import { BorderBox9 } from "@dataview/datav-vue3";
  import { useRouter, useRoute } from "vue-router";
  const router = useRouter();
  const route = useRoute();

  // 定义缺失的变量
  const logisticsMount = ref<boolean>(true);
  const marketingMount = ref<boolean>(true);

  // 切换
  const currentIndex = ref<number>(1);
  function routerChange(route: string) {
    router.push({ path: `/home/<USER>/car/${route}` });

    currentIndex.value == 2
      ? (currentIndex.value = 1)
      : (currentIndex.value = 2);
  }
</script>
<style lang="scss" scoped>
  .Board {
    box-sizing: border-box;
    display: flex;
    color: $processed;

    .tb-side-decoration-components {
      margin-left: 5px;
    }

    .board-right {
      .dv-border-box-9 {
        height: 85vh;
        width: 80vw;
      }
    }

    .btn-content {
      width: 20px;
      display: flex;
      flex-direction: column;
      margin-left: 10px;

      .el-button {
        --el-button-bg-color: #052658;
        border: 0 !important;
        --el-button-text-color: #7c9dcb;
        --el-button-hover-bg-color: #052658;
        --el-button-hover-text-color: #041c3f;
        height: 15vh !important;
        width: 2.2vw;
        font-size: 1.2vw !important;
      }
      .el-button:hover {
        color: #041c3f;
        background: #6e91c4;
      }

      .el-button.active {
        color: #041c3f;
        border: 0 !important;
        background-color: #6e91c4;
        outline: none;
      }

      .el-button + .el-button {
        margin-left: 0;
      }
    }
  }
</style>
