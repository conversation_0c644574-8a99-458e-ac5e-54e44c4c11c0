<template>
  <div class="addAreaDialog">
    <el-dialog
      v-model="addTransferOpen"
      title="添加对接点信息"
      width="50%"
      :close-on-click-modal="false"
      @close="closeAdd"
    >
      <el-form
        label-width="auto"
        class="areaForm"
        :model="totalFields"
        :rules="rules"
        ref="formRef"
      >
        <div class="flex">
          <el-form-item label="对接点名称" prop="transitDepotName">
            <el-input
              style="width: 200px"
              placeholder="请输入"
              v-model="totalFields.transitDepotName"
            ></el-input>
          </el-form-item>
          <el-form-item label="所属班组" prop="teamName">
            <el-select
              style="width: 200px"
              placeholder="请选择"
              v-model="totalFields.teamName"
              @change="getDeliveryInfo"
            >
              <el-option label="无" value="无" />
              <el-option
                :label="item.teamName"
                :value="item.teamName"
                v-for="item in store.transferInfo"
                :key="item.teamName + '9'"
              />
            </el-select>
          </el-form-item>
        </div>
        <div class="flex">
          <el-form-item label="启用状态" prop="status">
            <el-select
              style="width: 200px"
              placeholder="请选择"
              v-model="totalFields.status"
            >
              <el-option label="启用" value="启用" />
              <el-option label="禁用" value="禁用" />
            </el-select>
          </el-form-item>
          <el-form-item label="配送类型:" prop="deliveryType">
            <div>{{ totalFields.deliveryType }}</div>
          </el-form-item>
        </div>
        <div class="flex">
          <el-form-item label="对接配送域" prop="deliveryName">
            <el-select
              style="width: 200px"
              multiple
              placeholder="请选择"
              v-model="totalFields.deliveryName"
              @change="changeDelivery"
            >
              <el-option
                :label="item"
                :value="item"
                v-for="item in getDeliveryName"
                :key="item + '9'"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            label="经度"
            prop="longitude"
            style="margin-left: -40px"
          >
            <el-input
              style="width: 200px"
              placeholder="点击输入"
              v-model="totalFields.longitude"
            />
          </el-form-item>
          <el-form-item label="纬度" prop="latitude">
            <el-input
              style="width: 200px"
              placeholder="点击输入"
              v-model="totalFields.latitude"
            />
          </el-form-item>
        </div>
      </el-form>

      <div class="btns">
        <el-button type="primary" @click="closeAdd">取消</el-button>
        <el-button type="primary" style="margin-left: 100px" @click="confirmAdd"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { ref } from "vue";
  import { useTransferStore } from "@/store/managerment/transfer";
  const store = useTransferStore();
  const addTransferOpen = ref(false);
  const isTeamBlank = ref<boolean>(false);
  const formRef = ref<any>();
  defineExpose({
    addTransferOpen,
  });

  const validateStatus = (rule: any, value: any, callback: any) => {
    if (value === "启用" && isTeamBlank.value) {
      callback(new Error("班组为无!"));
    } else {
      formRef.value.clearValidate("status");
      callback();
    }
  };

  const rules = reactive({
    teamName: [{ required: true, message: "请选择", trigger: "change" }],
    transitDepotName: [
      {
        required: true,
        message: "请输入",
        trigger: "blur",
      },
    ],
    status: [
      {
        required: true,
        message: "请选择",
        trigger: "change",
      },
      {
        validator: validateStatus,
        trigger: "change",
      },
    ],
    longitude: [
      {
        required: true,
        message: "请输入经度",
        trigger: "blur",
      },
    ],
    latitude: [
      {
        required: true,
        message: "请输入纬度",
        trigger: "blur",
      },
    ],
  });

  const totalFields = ref<any>({
    deliveryName: [],
    deliveryType: "",
    latitude: "",
    longitude: "",
    status: "",
    teamName: "",
    transitDepotName: "",
  });
  const addEmit = defineEmits(["confirmAdd"]);

  const closeAdd = () => {
    formRef.value.resetFields();
    addTransferOpen.value = false;
  };

  async function confirmAdd() {
    if (!formRef.value) return;
    await formRef.value.validate((valid: any) => {
      if (valid) {
        if (totalFields.value.teamName === "无") {
          totalFields.value.teamName = "";
        }
        if (
          totalFields.value.status === "启用" &&
          totalFields.value.deliveryName.length === 0
        ) {
          ElMessage.error("状态为启用时, 配送域不能为空!");
          return;
        }
        totalFields.value.deliveryName =
          totalFields.value.deliveryName.join(",");
        store.addTransitDepotData(totalFields.value).then((res) => {
          formRef.value.resetFields();
          addTransferOpen.value = false;
          if (res.code === 50001 || res.message === "系统异常") {
            ElMessage.error("系统异常!");
            return;
          }
          addEmit("confirmAdd");
        });
      } else {
        ElMessage({
          message: "参数有错",
          type: "warning",
        });
      }
    });
  }

  const getDeliveryName = ref<string[]>([]);

  function getDeliveryInfo() {
    if (
      totalFields.value.teamName === "" ||
      totalFields.value.teamName === "无"
    ) {
      getDeliveryName.value = [];
      totalFields.value.deliveryType = "";
      isTeamBlank.value = true;
      return;
    }
    formRef.value.clearValidate("status");
    isTeamBlank.value = false;
    getDeliveryName.value = [];
    totalFields.value.deliveryName = [];
    store.transferInfo.forEach((item: any) => {
      if (item.teamName === totalFields.value.teamName) {
        if (item.deliveryName) {
          item.deliveryName.split(",").forEach((ele: any) => {
            if (!(ele === "" || !ele)) {
              getDeliveryName.value.push(ele);
            }
          });
        }
      }
    });
    const info = store.transferInfo.find(
      (item: any) => totalFields.value.teamName === item.teamName
    );
    totalFields.value.deliveryType = info.deliveryType;
  }

  function changeDelivery() {
    if (totalFields.value.deliveryName.includes("无")) {
      totalFields.value.deliveryName = ["无"];
    }
  }
</script>

<style lang="less" scoped>
  .flex {
    display: flex;
    flex-wrap: wrap;
  }

  .addArea {
    color: black;
  }
  .areaForm {
    .areaItem {
      font-size: 20px;
    }
  }
  .btns {
    display: flex;
    justify-content: center;
    color: black;
  }
</style>
