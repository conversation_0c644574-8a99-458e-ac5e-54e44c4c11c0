<template>
  <BorderBox9 :color="['#90ade8', '#90ade8']">
    <div class="system">
      <div class="content">
        <el-row>
          <el-col :span="12"
            ><div />
            <img
              src="../../../../assets/images/空心问号.png"
              alt=""
              width="16px"
              height="16px"
              @click="carSumDialogVisible = true"
            />
            车辆总数 {{ systemData.carSum }} 辆</el-col
          >
          <el-dialog v-model="carSumDialogVisible" width="500">
            <template #header="{ titleId, titleClass }">
              <div class="my-header">
                <div :id="titleId" :class="titleClass">
                  <img
                    src="../../../../assets/images/空心问号.png"
                    style="width: 16px"
                  />
                  <div
                    class="tip"
                    style="
                      display: flex;
                      flex-direction: column;
                      justify-content: center;
                      align-items: center;
                      text-align: center;
                      line-height: 2;
                    "
                  >
                    <span style="margin-bottom: 30px"
                      >如需更改请到车辆信息页面进行操作</span
                    >
                    <div style="display: flex">
                      <el-button
                        @click="carSumDialogVisible = false"
                        style="font-size: 16px"
                        >确认</el-button
                      >
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </el-dialog>
          <el-col :span="12"
            ><div />
            <img
              src="../../../../assets/images/空心问号.png"
              alt=""
              width="16px"
              height="16px"
              @click="freewayDialogVisible = true"
            />
            路径总数 {{ systemData.routeSum }} 条</el-col
          >
          <el-dialog v-model="freewayDialogVisible" width="500">
            <template #header="{ titleId, titleClass }">
              <div class="my-header">
                <div :id="titleId" :class="titleClass">
                  <img
                    src="../../../../assets/images/空心问号.png"
                    style="width: 16px"
                  />
                  <div
                    class="tip"
                    style="
                      display: flex;
                      flex-direction: column;
                      justify-content: center;
                      align-items: center;
                      text-align: center;
                      line-height: 2;
                    "
                  >
                    <span style="margin-bottom: 30px"
                      >如需更改请到配送域信息页面进行操作</span
                    >
                    <div style="display: flex">
                      <el-button
                        @click="freewayDialogVisible = false"
                        style="font-size: 16px"
                        >确认</el-button
                      >
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </el-dialog>
        </el-row>
        <el-row>
          <el-col :span="12" @click="teamSumDialogVisible = true"
            ><div />
            <img
              src="../../../../assets/images/空心问号.png"
              alt=""
              width="16px"
              height="16px"
              @click="teamSumDialogVisible = true"
            />
            班组总数 {{ systemData.teamSum }} 个</el-col
          >
          <el-dialog v-model="teamSumDialogVisible" width="500">
            <template #header="{ titleId, titleClass }">
              <div class="my-header">
                <div :id="titleId" :class="titleClass">
                  <img
                    src="../../../../assets/images/空心问号.png"
                    style="width: 16px"
                  />
                  <div
                    class="tip"
                    style="
                      display: flex;
                      flex-direction: column;
                      justify-content: center;
                      align-items: center;
                      text-align: center;
                      line-height: 2;
                    "
                  >
                    <span style="margin-bottom: 30px"
                      >如需更改请到班组信息页面进行操作</span
                    >
                    <div style="display: flex">
                      <el-button
                        @click="teamSumDialogVisible = false"
                        style="font-size: 16px"
                        >确认</el-button
                      >
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </el-dialog>
          <el-dialog v-model="jujiVisible" width="500">
            <template #header="{ titleId, titleClass }">
              <div class="my-header">
                <div :id="titleId" :class="titleClass">
                  <img
                    src="../../../../assets/images/空心问号.png"
                    style="width: 16px"
                  />
                  <div
                    class="tip"
                    style="
                      display: flex;
                      flex-direction: column;
                      justify-content: center;
                      align-items: center;
                      text-align: center;
                      line-height: 2;
                    "
                  >
                    <span style="margin-bottom: 30px"
                      >在一条路径中，以最多商铺的打卡点为基准，越少商铺的打卡点需要的卸货时长越长，反之更短。该系数只影响工作时长的计算，系数越大工作时长越长。</span
                    >
                    <div style="display: flex">
                      <el-button
                        @click="jujiVisible = false"
                        style="font-size: 16px"
                        >确认</el-button
                      >
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </el-dialog>
          <el-col :span="12"
            ><div class="flex" />
            <img
              src="../../../../assets/images/空心问号.png"
              alt=""
              width="16px"
              height="16px"
              @click="jujiVisible = true" />
            <span style="padding-left: 4px">聚集区密集度系数</span
            ><el-input
              style="
                width: 100px;
                height: 25px;
                margin-left: 10px;
                padding-top: 5px;
              "
              placeholder="点击输入"
              v-model="accumulationIntensity"
              @keyup.enter="dialogVisible1 = true"
          /></el-col>
          <el-dialog v-model="dialogVisible1" title="提醒" width="500">
            <div
              class="tip"
              style="
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                text-align: center;
                line-height: 2;
              "
            >
              <span style="margin-bottom: 30px">确认修改？</span>
              <div style="display: flex">
                <el-button @click="updateMessage" style="font-size: 16px"
                  >确认</el-button
                ><el-button
                  @click="dialogVisible1 = false"
                  style="font-size: 16px"
                  >取消</el-button
                >
              </div>
            </div>
          </el-dialog>
          <el-dialog v-model="dialogVisible2" title="提醒" width="500">
            <div
              class="tip"
              style="
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                text-align: center;
                line-height: 2;
              "
            >
              <span style="margin-bottom: 30px"
                >聚集区密集度系数已更变，<br />请到聚集区计算页面进行重新计算<br /><span
                  style="font-size: 12px; color: red"
                  >如何重新计算
                  <img
                    src="../../../../assets/images/空心问号.png"
                    style="width: 14px" /></span
              ></span>
              <el-button @click="updateMessage" style="font-size: 16px"
                >确认</el-button
              >
            </div>
          </el-dialog>
          <el-col :span="24"
            >商铺平均卸货时长（分钟）：<span>城区</span
            ><el-input
              v-model="shoreUnloadCityTime"
              style="
                width: 150px;
                height: 25px;
                margin-left: 10px;
                padding-top: 5px;
              "
              placeholder="点击输入"
              @keyup.enter="shoreUnloadCityTimeDialogVisible = true"
            />
            <el-dialog v-model="shoreUnloadCityTimeDialogVisible" width="500">
              <div
                class="tip"
                style="
                  display: flex;
                  flex-direction: column;
                  justify-content: center;
                  align-items: center;
                  text-align: center;
                  line-height: 2;
                "
              >
                <span style="margin-bottom: 30px">确认修改？</span>
                <div style="display: flex">
                  <el-button @click="updateMessage" style="font-size: 16px"
                    >确认</el-button
                  ><el-button
                    @click="shoreUnloadCityTimeDialogVisible = false"
                    style="font-size: 16px"
                    >取消</el-button
                  >
                </div>
              </div>
            </el-dialog>
            <span style="margin-left: 30px">乡镇</span
            ><el-input
              v-model="shoreUnloadTownshipTime"
              style="
                width: 150px;
                height: 25px;
                margin-left: 10px;
                padding-top: 5px;
              "
              @keyup.enter="shoreUnloadTownshipTimeDialogVisible = true"
              placeholder="点击输入"
            />
            <el-dialog
              v-model="shoreUnloadTownshipTimeDialogVisible"
              width="500"
            >
              <div
                class="tip"
                style="
                  display: flex;
                  flex-direction: column;
                  justify-content: center;
                  align-items: center;
                  text-align: center;
                  line-height: 2;
                "
              >
                <span style="margin-bottom: 30px">确认修改？</span>
                <div style="display: flex">
                  <el-button @click="updateMessage" style="font-size: 16px"
                    >确认</el-button
                  ><el-button
                    @click="shoreUnloadTownshipTimeDialogVisible = false"
                    style="font-size: 16px"
                    >取消</el-button
                  >
                </div>
              </div>
            </el-dialog>
            <span style="margin-left: 30px">装车时长（分钟）: </span>
            <el-input
              v-model="loadingTime"
              style="
                width: 150px;
                height: 25px;
                margin-left: 10px;
                padding-top: 5px;
              "
              placeholder="点击输入"
              @keyup.enter="carVisble = true"
            />
          </el-col>
          <el-dialog v-model="carVisble" width="500">
            <div
              class="tip"
              style="
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                text-align: center;
                line-height: 2;
              "
            >
              <span style="margin-bottom: 30px">确认修改？</span>
              <div style="display: flex">
                <el-button @click="updateMessage" style="font-size: 16px"
                  >确认</el-button
                ><el-button @click="carVisble = false" style="font-size: 16px"
                  >取消</el-button
                >
              </div>
            </div>
          </el-dialog>
          <el-col :span="24"
            >车辆时速（千米每时）：<span>高速公路</span
            ><el-input
              v-model="freeway"
              style="
                width: 150px;
                height: 25px;
                margin-left: 10px;
                padding-top: 5px;
              "
              @keyup.enter="speedDialogVisible = true"
              placeholder="点击输入"
            /><span style="margin-left: 30px">城区公路</span
            ><el-input
              v-model="urbanRoads"
              style="
                width: 140px;
                height: 25px;
                margin-left: 10px;
                padding-top: 5px;
              "
              @keyup.enter="speedDialogVisible = true"
              placeholder="点击输入"
            />
            <span style="margin-left: 30px">乡镇公路</span>
            <el-input
              v-model="townshipRoads"
              style="
                width: 80px;
                height: 25px;
                padding-top: 5px;
                margin-left: 20px;
              "
              @keyup.enter="speedDialogVisible = true"
              placeholder="点击输入"
            />
            <el-dialog v-model="speedDialogVisible" width="500">
              <div
                class="tip"
                style="
                  display: flex;
                  flex-direction: column;
                  justify-content: center;
                  align-items: center;
                  text-align: center;
                  line-height: 2;
                "
              >
                <span style="margin-bottom: 30px">确认修改？</span>
                <div style="display: flex">
                  <el-button @click="updateMessage" style="font-size: 16px"
                    >确认</el-button
                  ><el-button
                    @click="speedDialogVisible = false"
                    style="font-size: 16px"
                    >取消</el-button
                  >
                </div>
              </div>
            </el-dialog></el-col
          >
          <el-col :span="24"
            ><img
              src="../../../../assets/images/空心问号.png"
              alt=""
              width="16px"
              height="16px"
              @click="levelDialogVisible = true"
            />档位设置 :<el-input
              v-model="excludeGearNum"
              style="
                width: 150px;
                height: 25px;
                margin-left: 10px;
                padding-top: 5px;
              "
              @keyup.enter="dialogVisible3 = true"
              placeholder="点击输入"
            />
          </el-col>
          <el-dialog v-model="levelDialogVisible" width="500">
            <template #header="{ titleId, titleClass }">
              <div class="my-header">
                <div :id="titleId" :class="titleClass">
                  <img
                    src="../../../../assets/images/空心问号.png"
                    style="width: 16px"
                  />
                  <div
                    class="tip"
                    style="
                      display: flex;
                      flex-direction: column;
                      justify-content: center;
                      align-items: center;
                      text-align: center;
                      line-height: 2;
                    "
                  >
                    <span style="margin-bottom: 30px"
                      >档位设置: 商户档位大于等于输入参数地, 定点取货的权值为0,
                      直送到户, 无需定点取货.</span
                    >
                    <div style="display: flex">
                      <el-button
                        @click="levelDialogVisible = false"
                        style="font-size: 16px"
                        >确认</el-button
                      >
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </el-dialog>
          <el-dialog v-model="dialogVisible3" title="提醒" width="500">
            <div
              class="tip"
              style="
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                text-align: center;
                line-height: 2;
              "
            >
              <span style="margin-bottom: 30px">确认修改？</span>
              <div style="display: flex">
                <el-button @click="updateMessage" style="font-size: 16px"
                  >确认</el-button
                ><el-button
                  @click="dialogVisible3 = false"
                  style="font-size: 16px"
                  >取消</el-button
                >
              </div>
            </div>
          </el-dialog>
          <el-col :span="24">
            <img
              src="../../../../assets/images/空心问号.png"
              style="width: 16px"
              @click="secondTransitListDialogVisible = true"
            />二次中转时长（分钟）：
            <template
              v-for="(itemInfo, index) in useSystemStore.systemInfo
                .secondTransitList"
              :key="itemInfo"
            >
              <div>
                <div style="margin-top: 10px">
                  {{ itemInfo.secondTransitName }} :
                  <el-input
                    v-model="itemInfo.transitTime"
                    style="
                      width: 140px;
                      height: 25px;
                      margin-left: 10px;
                      padding-top: 5px;
                    "
                    @keyup="updateSecondDialogVisible = true"
                    placeholder="点击输入"
                  />
                  <el-button
                    size="small"
                    type="primary"
                    style="margin-left: 10px; margin-bottom: 10px; color: black"
                    @click="deleteSecond(index)"
                    >删除</el-button
                  >
                </div>
              </div>
            </template>

            <el-dialog v-model="updateSecondDialogVisible" width="500">
              <div
                class="tip"
                style="
                  display: flex;
                  flex-direction: column;
                  justify-content: center;
                  align-items: center;
                  text-align: center;
                  line-height: 2;
                "
              >
                <span style="margin-bottom: 30px">确认修改？</span>
                <div style="display: flex">
                  <el-button
                    type="primary"
                    @click="updateMessage"
                    style="font-size: 16px"
                    >确认</el-button
                  ><el-button
                    type="primary"
                    @click="updateSecondDialogVisible = false"
                    style="font-size: 16px"
                    >取消</el-button
                  >
                </div>
              </div>
            </el-dialog>

            <el-button
              type="primary"
              class="addElementButton"
              @click="SecondDialogFormVisible = true"
              style="margin-left: 10px"
              >点击新增</el-button
            >

            <el-dialog
              v-model="SecondDialogFormVisible"
              title="添加二次中转时长（分钟）"
              width="500"
            >
              <el-form>
                <el-form-item label="二次中转站名称：">
                  <el-select
                    v-model="secondTransitName"
                    placeholder="Select"
                    style="width: 240px"
                  >
                    <el-option label="马市烟叶工作站" value="马市烟叶工作站" />
                    <el-option
                      label="班组一物流配送中心"
                      value="班组一物流配送中心"
                    />
                    <el-option label="坪石镇中转站" value="坪石镇中转站" />
                    <el-option
                      label="班组二物流配送中心"
                      value="班组二物流配送中心"
                    />
                    <el-option label="翁源县中转站" value="翁源县中转站" />
                  </el-select>
                </el-form-item>
                <el-form-item label="二次中转时长：">
                  <el-input v-model="transitTime" type="text" />
                </el-form-item>
              </el-form>
              <template #footer>
                <div class="dialog-footer">
                  <el-button @click="SecondDialogFormVisible = false"
                    >取消</el-button
                  >
                  <el-button type="primary" @click="addSecond">
                    确认
                  </el-button>
                </div>
              </template>
            </el-dialog>
          </el-col>
          <el-dialog v-model="secondTransitListDialogVisible" width="500">
            <template #header="{ titleId, titleClass }">
              <div class="my-header">
                <div :id="titleId" :class="titleClass">
                  <img
                    src="../../../../assets/images/空心问号.png"
                    style="width: 16px"
                  />二次中转时长：
                  <div
                    class="tip"
                    style="
                      display: flex;
                      flex-direction: column;
                      justify-content: center;
                      align-items: center;
                      text-align: center;
                      line-height: 2;
                    "
                  >
                    <span style="margin-bottom: 30px"
                      >中转站路线除了指定配送域，还包含其他对接中转站（该二级中转站位于一级中转站对接配送域内）的运送货物工作，后者产生的工作时长即为二次中转时长</span
                    >
                    <div style="display: flex">
                      <el-button
                        @click="secondTransitListDialogVisible = false"
                        style="font-size: 16px"
                        >确认</el-button
                      >
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </el-dialog>
        </el-row>
      </div>
    </div>
  </BorderBox9>
</template>

<script setup lang="ts">
  import { BorderBox9 } from "@dataview/datav-vue3";
  import { systemStore } from "@/store/managerment/system";
  import { ref } from "vue";
  import { ElMessage } from "element-plus";
  import { usePickStore } from "@/store/pick";
  const pickupStore = usePickStore();
  const balanceForm = reactive({});

  export interface ISystemDelete {
    id: number;
  }

  let systemData = ref<object>({
    carSum: "",
    freeway: "",
    teamSum: "",
  });

  let carSumDialogVisible = ref<boolean>(false);
  let freewayDialogVisible = ref<boolean>(false);
  let teamSumDialogVisible = ref<boolean>(false);
  let dialogVisible1 = ref<boolean>(false);
  let dialogVisible2 = ref<boolean>(false);
  const dialogVisible3 = ref<boolean>(false);
  let speedDialogVisible = ref<boolean>(false);
  let levelDialogVisible = ref<boolean>(false);
  const jujiVisible = ref<boolean>(false);
  const carVisble = ref<boolean>(false);
  let secondTransitListDialogVisible = ref<boolean>(false);
  let shoreUnloadCityTimeDialogVisible = ref<boolean>(false);
  let shoreUnloadTownshipTimeDialogVisible = ref<boolean>(false);
  let updateSecondDialogVisible = ref<boolean>(false);

  // 参数信息
  let accumulationIntensity = ref<number>();
  let freeway = ref<number>();
  let Id = ref<number>();

  // 增加二次中转时长
  // let id = ref<number>();
  let secondTransitName = ref<string>();
  let transitTime = ref<number>();

  let shoreUnloadCityTime = ref<number>();
  let shoreUnloadTownshipTime = ref<number>();
  let townshipRoads = ref<number>();
  let urbanRoads = ref<number>();
  let loadingTime = ref<number>();
  let excludeGearNum = ref<number>();

  const useSystemStore = systemStore();
  onMounted(async () => {
    const params = await pickupStore.getParams();
    const { gear, excludeGear, avgDistance, roadGrade, levelParam } = params;
    excludeGearNum.value = excludeGear;
    balanceForm.gear = gear;
    balanceForm.excludeGear = excludeGear;
    balanceForm.avgDistance = avgDistance;
    balanceForm.roadGrade = roadGrade;
    balanceForm.levelParam = levelParam;
    getData();
  });

  // 删除二次中转时长
  function deleteSecond(index: any) {
    const deleteId = useSystemStore.systemInfo.secondTransitList[index].id;
    const data: ISystemDelete = {
      id: Number(deleteId),
    };

    useSystemStore.deleteSystemData(data).then((res) => {
      if (res.code == 200) {
        ElMessage({
          message: "删除成功",
          type: "success",
        });
        getData();
      } else {
        ElMessage.error({
          message: "删除失败",
        });
      }
    });
    console.log(useSystemStore.systemInfo.secondTransitList[index]);
  }

  // 添加二次中转时长
  let SecondDialogFormVisible = ref(false);

  function getData() {
    useSystemStore.getSystemData().then(() => {
      systemData.value = useSystemStore.systemInfo;
      accumulationIntensity.value =
        useSystemStore.systemInfo.accumulationIntensity;
      freeway.value = useSystemStore.systemInfo.freeway;
      Id.value = useSystemStore.systemInfo.id;
      shoreUnloadCityTime.value = useSystemStore.systemInfo.shoreUnloadCityTime;
      shoreUnloadTownshipTime.value =
        useSystemStore.systemInfo.shoreUnloadTownshipTime;
      townshipRoads.value = useSystemStore.systemInfo.townshipRoads;
      urbanRoads.value = useSystemStore.systemInfo.urbanRoads;
      loadingTime.value = useSystemStore.systemInfo.loadingTime;
      console.log(useSystemStore.systemInfo.secondTransitList);
    });
  }

  function addSecond() {
    SecondDialogFormVisible.value = false;
    useSystemStore.systemInfo.secondTransitList.push({
      // id: Number(id.value),
      secondTransitName: secondTransitName.value,
      transitTime: Number(transitTime.value),
    });
    updateMessage().then((res) => {
      if (res === "成功") {
        ElMessage({
          message: "添加成功",
          type: "success",
        });
      } else {
        ElMessage.error("添加失败");
      }
      getData();
    });
  }

  async function updateMessage() {
    carVisble.value = false;
    jujiVisible.value = false;
    shoreUnloadCityTimeDialogVisible.value = false;
    shoreUnloadTownshipTimeDialogVisible.value = false;
    speedDialogVisible.value = false;
    if (updateSecondDialogVisible.value) {
      updateSecondDialogVisible.value = false;
    }
    if (dialogVisible2.value) {
      dialogVisible2.value = false;
      ElMessage.success("修改成功");
      return;
    }
    if (dialogVisible3.value) {
      dialogVisible3.value = false;
      const loading = ElLoading.service({
        lock: true,
        text: "Loading",
        background: "rgba(0, 0, 0, 0.7)",
      });
      const params = await pickupStore.getParams();
      const { gear, avgDistance, roadGrade, levelParam } = params;
      balanceForm.gear = gear;
      balanceForm.avgDistance = avgDistance;
      balanceForm.roadGrade = roadGrade;
      balanceForm.levelParam = levelParam;

      const res = await pickupStore.updateParamsList({
        gear: Number(gear),
        avgDistance: Number(avgDistance),
        roadGrade: Number(roadGrade),
        levelParam: Number(levelParam),
        excludeGear: excludeGearNum.value,
      });

      if (res.msg && res.msg.includes("成功")) {
        ElMessage.success(res.msg);
      } else {
        ElMessage.error("修改失败");
      }
      loading.close();
      return;
    }
    if (dialogVisible1.value) {
      dialogVisible1.value = false;
      dialogVisible2.value = true;
    }
    ElMessage.success("修改成功");

    const res: any = await useSystemStore.updateSystemData({
      accumulationIntensity: accumulationIntensity.value,
      freeway: freeway.value,
      id: Id.value,
      secondTransitList: useSystemStore.systemInfo.secondTransitList,
      shoreUnloadCityTime: shoreUnloadCityTime.value,
      shoreUnloadTownshipTime: shoreUnloadTownshipTime.value,
      townshipRoads: townshipRoads.value,
      urbanRoads: urbanRoads.value,
      loadingTime: loadingTime.value,
    });

    if (res == "操作成功") {
      return "成功";
    } else {
      console.log(res);
      // throw new Error("失败");
    }
  }
</script>

<style lang="less" scoped>
  .system {
    .el-button,
    .el-button:focus:not(.el-button:hover) {
      --el-color-primary: #003766;
      --el-button-bg-color: #97c7e7;
      --el-button-border-color: none;
      --el-button-text-color: #003766;
    }
    width: 100%;
    height: 100%;
    color: white;
    display: flex;
    justify-content: center;
    align-items: center;

    .content {
      width: 90%;
      height: 100%;
      color: #aaedfe;
      font-weight: 700;
      font-size: 20px;

      div {
        margin-bottom: 15px;
      }
    }
  }
</style>
