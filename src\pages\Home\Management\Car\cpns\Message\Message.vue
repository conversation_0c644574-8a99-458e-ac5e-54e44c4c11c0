<template>
  <div class="to-messages">
    <div style="display: flex">
      <div class="top">
        <el-input class="input" placeholder="请点击搜索" @click="openSearch" />
        <el-button
          class="button"
          :icon="Plus"
          @click="openAdd"
          v-if="hasOp('data-management:car:add')"
          >添加车辆</el-button
        >
        <el-button
          class="button"
          :icon="Delete"
          @click="openDelete"
          v-if="hasOp('data-management:car:delete')"
          >删除车辆</el-button
        >
        <el-button
          class="button"
          :icon="EditPen"
          @click="openChange"
          v-if="hasOp('data-management:car:update')"
          >修改信息</el-button
        >
        <div class="search" v-if="searchOpen">
          <div class="off" @click="closeSearch">x</div>
          <el-form class="form" :model="searchFormData" ref="searchModal">
            <el-form-item
              label="车牌号"
              style="width: 45%"
              prop="searchCarNumber"
            >
              <el-select
                style="width: 250px"
                placeholder="请选择"
                v-model="searchFormData.searchCarNumber"
              >
                <el-option
                  :label="item"
                  :value="item"
                  v-for="item in store.carSearchInfo.licensePlateNumberList"
                  :key="item + '9'"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              label="班组"
              style="width: 47%; margin-left: 20px"
              prop="searchTeamName"
            >
              <el-select
                style="width: 250px"
                placeholder="请选择"
                v-model="searchFormData.searchTeamName"
              >
                <el-option
                  :label="item"
                  :value="item"
                  v-for="item in store.carSearchInfo.teamList"
                  :key="item + '9'"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="驾驶人" style="width: 45%" prop="searchDriver">
              <el-select
                style="width: 250px"
                placeholder="请选择"
                v-model="searchFormData.searchDriver"
              >
                <el-option
                  :label="item"
                  :value="item"
                  v-for="item in Object.keys(store.carSearchInfo.carDriverList)"
                  :key="item + '9'"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              label="状态"
              style="width: 47%; margin-left: 20px"
              prop="searchStatus"
            >
              <el-select
                style="width: 100px"
                placeholder="请输入"
                v-model="searchFormData.searchStatus"
              >
                <el-option
                  :label="item"
                  :value="item"
                  v-for="item in store.carSearchInfo.statusList"
                  :key="item + '9'"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              label="最大载重"
              style="width: 100%"
              prop="searchMaxLoad"
            >
              <el-input
                style="width: 100px"
                placeholder="请输入"
                v-model="searchFormData.searchMaxLoad"
              />
            </el-form-item>
            <div class="btn-content">
              <el-button @click="resetSearch">清空</el-button>
              <el-button @click="confirmSearch">搜索</el-button>
            </div>
          </el-form>
        </div>
      </div>
    </div>

    <div class="main" v-if="hasOp('data-management:car:view')">
      <el-table
        ref="tableRef"
        :data="carList"
        :cell-style="{ textAlign: 'center' }"
        @selection-change="handleSelect"
        :header-cell-style="{
          height: '4vh',
          'text-align': 'center',
        }"
        size="small"
        :row-style="{ height: '4.3vh' }"
        style="font-size: 0.8vw"
      >
        <el-table-column type="selection" min-width="1%" />
        <el-table-column
          label="序号"
          min-width="1%"
          type="index"
          :index="Nindex"
        />
        <el-table-column
          label="车牌号"
          min-width="3%"
          prop="licensePlateNumber"
        />
        <el-table-column label="驾驶人名称" min-width="3%" prop="userName" />
        <el-table-column label="驾驶人电话" min-width="2%" prop="phone" />
        <el-table-column
          label="最大载重 (吨)"
          min-width="2.5%"
          prop="maxLoad"
        />
        <el-table-column label="状态" min-width="2%" prop="status" />
        <el-table-column
          label="所属配送域"
          min-width="3%"
          prop="deliveryAreaName"
        >
          <template #default="scope">
            <div>
              {{
                scope.row.deliveryAreaName ? scope.row.deliveryAreaName : "无"
              }}
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="dialog">
      <addCar ref="addCarRef" @confirmAdd="confirmAdd" />
      <deleteCar
        ref="deleteCarRef"
        @confirmDelete="confirmDelete"
        :carId="carId"
      ></deleteCar>
      <changeCar
        v-if="info"
        ref="changeCarRef"
        @confirmChange="confirmChange"
        :carId="carId"
        :info="info"
        :carName="carName"
      />
      <pathDialog ref="pathRef" dType="车辆"></pathDialog>
    </div>
  </div>
  <div class="pa">
    <el-pagination
      v-if="!inSearch"
      layout="prev, pager, next"
      :current-page="searchData.pageNum"
      :page-size="searchData.pageSize"
      :total="store.carTotalList.length"
      @current-change="handlePageChange"
    />
    <el-pagination
      v-if="inSearch"
      layout="prev, pager, next"
      :current-page="searchData.pageNum"
      :page-size="searchData.pageSize"
      :total="carList.length"
      @current-change="handlePageChange"
    />
  </div>
</template>

<script lang="ts" setup>
  import { hasOp } from "@/op";
  import { Plus, Delete, EditPen } from "@element-plus/icons-vue";
  import addCar from "./addCar/addCar.vue";
  import changeCar from "./changeCar/changeCar.vue";
  import deleteCar from "./deleteCar/deleteCar.vue";
  import pathDialog from "../../../Path/pathDialog.vue";
  import { carStore } from "@/store/managerment/car";

  onMounted(() => {
    store.getAllCarData(searchData.pageSize).then(() => {
      getTableInitData(searchData.pageSize);
    });
  });

  const searchFormData = ref<any>({
    searchCarNumber: "",
    searchTeamName: "",
    searchDriver: "",
    searchStatus: "",
    searchMaxLoad: "",
  });
  const searchResult = ref<any>([]);
  const inSearch = ref<boolean>(false);
  const searchOpen = ref<boolean>(false);
  const addCarRef = ref<any>();
  const deleteCarRef = ref<any>();
  const changeCarRef = ref<any>();
  const tableRef = ref<any>();
  const carName = ref<string>();
  const carId = ref<number>(-1);
  const store = carStore();
  //数据
  const carList = ref<any>([]);

  //搜索对象
  const searchData = reactive({
    pageNum: 1,
    pageSize: 14,
  });
  const searchModal = ref<any>();

  function openSearch() {
    store.searchCarInfoData().then(() => {
      searchOpen.value = true;
    });
  }
  function closeSearch() {
    searchOpen.value = false;
  }

  function resetSearch() {
    inSearch.value = false;
    searchFormData.value = {
      searchCarNumber: "",
      searchTeamName: "",
      searchDriver: "",
      searchStatus: "",
      searchMaxLoad: "",
    };
    searchData.pageNum = 1;
    getData({
      ...searchData,
    });
  }

  function confirmSearch() {
    inSearch.value = true;
    searchData.pageNum = 1;
    getData({
      ...searchData,
      carDriver: searchFormData.value.searchDriver,
      licensePlateNumber: searchFormData.value.searchCarNumber,
      maxLoad: searchFormData.value.searchMaxLoad,
      status: searchFormData.value.searchStatus,
      teamName: searchFormData.value.searchTeamName,
    });
  }

  //打开添加弹窗
  function openAdd() {
    if (inSearch.value) {
      ElMessage({
        message: "还在搜索状态中,禁止打开弹窗",
        type: "warning",
      });
      return;
    }
    store.addCarInfoData().then(() => {
      addCarRef.value.addCarOpen = true;
    });
  }

  function openDelete() {
    if (inSearch.value) {
      ElMessage({
        message: "还在搜索状态中,禁止打开弹窗",
        type: "warning",
      });
      return;
    }
    if (tableRef.value.getSelectionRows().length !== 1) {
      ElMessage({
        message: "请正确选择一个车辆删除!",
        type: "warning",
      });
      return;
    }
    if (tableRef.value.getSelectionRows()[0].status !== "异常") {
      ElMessage({
        message: "请选择异常车辆删除!",
        type: "warning",
      });
      return;
    }
    deleteCarRef.value.deleteVis = true;
  }
  const pathRef = ref<any>();
  //确定添加车辆的信息
  function confirmAdd(data: any) {
    addCarRef.value.addCarOpen = false;
    if (
      store.carTotalList.findIndex(
        (item: any) => item.licensePlateNumber === data.licensePlateNumber
      ) !== -1
    ) {
      ElMessage({
        message: "添加失败!有相同名字",
        type: "warning",
      });
      return;
    }
    store.addCarData(data).then((res) => {
      if (res.code === 50001 || res.message === "系统异常") {
        ElMessage.error("系统异常!");
        return;
      }
      pathRef.value.vis = true;
      store.getAllCarData(searchData.pageSize).then(() => {
        pageQuery(searchData.pageNum, searchData.pageSize);
        ElMessage({
          message: "添加成功!",
          type: "success",
        });
      });
    });
  }

  //确定删除
  function confirmDelete() {
    deleteCarRef.value.deleteVis = false;
    store.getAllCarData(searchData.pageSize).then(() => {
      pageQuery(searchData.pageNum, searchData.pageSize);
      ElMessage({
        message: "删除成功!",
        type: "success",
      });
    });
  }

  // 打开修改弹窗
  function openChange() {
    if (tableRef.value.getSelectionRows().length !== 1) {
      ElMessage({
        message: "请单选一个班组修改",
        type: "warning",
      });
      return;
    }
    store.addCarInfoData().then(() => {
      changeCarRef.value.changeCarOpen = true;
    });
  }

  const current = ref<number>(0);
  //确认修改弹窗
  function confirmChange() {
    changeCarRef.value.changeCarOpen = false;
    pathRef.value.vis = true;
    current.value = searchData.pageNum;
    store.getAllCarData(searchData.pageSize).then(() => {
      searchData.pageNum = current.value;
      pageQuery(searchData.pageNum, searchData.pageSize);
      ElMessage({
        message: "修改成功!",
        type: "success",
      });
    });
  }

  const info = ref<any>();
  //处理选择行
  function handleSelect(rowData: any) {
    if (rowData.length >= 1) {
      carId.value = rowData[0].carId;
      carName.value = rowData[0].licensePlateNumber;
      info.value = Object.assign({}, rowData[0]);
      if (!info.value.deliveryAreaName) {
        info.value.deliveryAreaName = "";
      }
      if (!info.value.teamName) {
        info.value.teamName = "";
      }
    }
  }

  //处理分页
  function handlePageChange(num: number = 1) {
    searchData.pageNum = num;
    if (inSearch.value) {
      carList.value = searchResult.value.slice(
        searchData.pageNum * searchData.pageSize - searchData.pageSize,
        searchData.pageNum * searchData.pageSize
      );
      return;
    }
    pageQuery(searchData.pageNum, searchData.pageSize);
  }

  //传入分页参数
  function pageQuery(pageNum: number, pageSize: number) {
    if (inSearch.value) {
      confirmSearch();
      return;
    }
    carList.value = store.carTotalList.slice(
      pageNum * pageSize - pageSize,
      pageNum * pageSize
    );
  }

  //获取初始表格数据
  function getTableInitData(pageSize: number) {
    carList.value = store.carTotalList.slice(0, pageSize);
  }

  function getData(data: any) {
    store.getCarData(data).then((res: any) => {
      if (res.code === 50001 || res.message === "系统异常") {
        ElMessage.error("系统异常!");
        return;
      }
      console.log(data);
      carList.value = res.data.records;
    });
  }

  function Nindex(index: number) {
    const page = searchData.pageNum; // 当前页码
    const pagesize = searchData.pageSize; // 每页条数
    return index + 1 + (page - 1) * pagesize;
  }
</script>
<style lang="scss" scoped>
  .to-messages {
    // :deep(.el-button) {
    //   color: #7c9dcb;
    //   border: 2px solid #7c9dcb !important;
    // }
    .input {
      width: 400px;
      margin-left: 20px;
      margin-top: 10px;
    }
    .button {
      margin-left: 10px;
      width: max-content;
      height: 30px;
      margin-top: 10px;
    }
    .top {
      margin: 1vh 0px;

      .select {
        margin: 0 1vw;
      }

      .btn {
        box-sizing: border-box;
        width: 2.3vw;
        height: 4vh;
      }

      .search {
        position: absolute;
        z-index: 9;
        background-color: #091b3a;
        width: 500px;

        .off {
          width: 2vw;
          height: 3vh;
          position: absolute;
          right: 0;
          color: white;
          font-size: 20px;
          text-align: center;
          cursor: pointer;
          z-index: 10;
        }

        .form {
          display: flex;
          flex-wrap: wrap;
          padding: 20px;
          align-items: center;
          border: 1px solid #9addf6;
        }

        .btn-content {
          margin: 0 auto;
        }

        .el-form-item {
          width: 33.3%;
        }
      }
    }

    .second {
      margin: 1vh;

      .btn {
        margin: 0 10px;
        box-sizing: border-box;
        width: max-content;
        // width: 6.5vw;
        height: 4vh;
      }
    }

    .main {
      :deep(.el-table__header th) {
        background-color: #9addf6;
      }
    }
  }

  .el-pagination {
    display: flex;
    justify-content: center;
  }

  .info {
    height: 4.5vh;
    font-size: 20px;
    color: rgb(204, 255, 255);
    margin-bottom: 3vh;
  }

  .confirmArea {
    margin-top: 10vh;
    width: 100px;
    position: relative;
    left: 50%;
    transform: translate(-50%);
  }

  .pa {
    position: absolute;
    bottom: -30px;
    left: 50%;
    transform: translate(-50%);
  }
  :deep(.el-pagination) {
    margin-bottom: 0;
  }
</style>
