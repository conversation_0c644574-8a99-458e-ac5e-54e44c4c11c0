<template>
  <el-dialog
    v-model="confirmDialogVis"
    title="批量配置"
    class="transform"
    width="40%"
    align-center
    :close-on-click-modal="false"
  >
    <div class="content">确定为待分配商户配置相对应的最近选址</div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancelDialog" type="primary">取消</el-button>
        <el-button type="primary" @click="confirmDialog">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
  const waitWait = defineEmits(["wait"]);
  //弹窗的可视化
  const confirmDialogVis = ref<boolean>(false);
  defineExpose({
    confirmDialogVis,
  });
  const props = defineProps<{
    list: any;
  }>();

  function confirmDialog() {
    waitWait("wait", props.list);
    confirmDialogVis.value = false;
  }

  //取消变更
  function cancelDialog() {
    confirmDialogVis.value = false;
  }
</script>

<style scoped></style>
